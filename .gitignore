# Python
__pycache__/
*.py[cod]
*$py.class
*.so
*.pyc
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Flask
instance/
.webassets-cache
flask_session/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Logs
*.log

# Testing
.coverage
.pytest_cache/
.tox/

# Documentation builds
docs/_build/

# Temporary files
*.tmp
*.bak
*.backup
