# GRS Wall Designer - Deployment Guide

## Configuration System

The application now uses a centralized configuration system with two environments:

### Development Environment
- **File**: `config.py` - `DevelopmentConfig` class
- **Features**: Debug mode enabled, detailed logging, auto-reload
- **Database**: Local MySQL with default credentials
- **Security**: Relaxed cookie settings for development

### Production Environment
- **File**: `config.py` - `ProductionConfig` class
- **Features**: Debug disabled, critical-only logging, optimized for performance
- **Database**: Same credentials as development (localhost MySQL)
- **Security**: Secure cookie settings, HTTPS ready
- **Platform**: Linux only

## Running the Application

### Development Mode
```bash
# Option 1: Direct execution (default is development)
python app.py

# Option 2: Set environment explicitly and run app
export FLASK_ENV=development
python app.py

# Option 3: Using Flask CLI
export FLASK_ENV=development
flask run --host=0.0.0.0 --port=5000
```

### Production Mode
```bash
# Recommended: Use the production runner (sets environment internally)
python run_production.py

# Direct gunicorn command
gunicorn app:app --bind 0.0.0.0:5000 --workers 1 --timeout 120

# Manual production mode
export FLASK_ENV=production
python app.py
```

## Production Runner Features

The `run_production.py` script provides a robust deployment solution:

1. **Primary**: Attempts to start gunicorn production server
2. **Fallback 1**: If gunicorn fails, starts Flask development server
3. **Fallback 2**: If both fail, serves a maintenance page

### Production Server Command
```bash
gunicorn app:app --bind 0.0.0.0:5000 --workers 1 --timeout 120
```

## Environment Variables

For development mode (optional, defaults to development):
```bash
export FLASK_ENV=development
```

For production mode (optional, `run_production.py` sets this automatically):
```bash
export FLASK_ENV=production
```

Note: Database credentials are the same for both development and production (localhost MySQL).

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure MySQL is running and database exists

3. Run the application:
```bash
# Development
python app.py

# Production
python run_production.py
```

## File Structure

```
├── config.py                 # Centralized configuration
├── app.py                    # Main Flask application
├── run_production.py         # Production server with fallbacks
├── requirements.txt          # Python dependencies
├── templates/
│   └── maintenance.html      # Maintenance page template
└── logs/                     # Application logs
    ├── grs_app.log          # Main application log
    ├── production_runner.log # Production runner log
    ├── gunicorn_access.log  # Gunicorn access log
    └── gunicorn_error.log   # Gunicorn error log
```

## Configuration Details

### Database Configuration
- **Development**: Uses local MySQL credentials (localhost)
- **Production**: Uses same local MySQL credentials (localhost)

### Logging Configuration
- **Development**: INFO level, console + file output
- **Production**: CRITICAL level only, file output only

### Session Configuration
- **Development**: HTTP cookies allowed
- **Production**: HTTPS-only secure cookies

### Cache and Cleanup
- **Cache Timeout**: 30 seconds (configurable)
- **Cleanup Interval**: 60 seconds (configurable)

## Maintenance Page

If both production and development servers fail, a maintenance page is served with:
- Professional styling
- Auto-refresh every 30 seconds
- Contact information
- Status indicators

## Troubleshooting

### Gunicorn Not Found
```bash
pip install gunicorn
```

### Port Already in Use
The production runner checks port availability and will show appropriate errors.

### Database Connection Issues
Check MySQL service and credentials in config.py or environment variables.

### Logs Location
All logs are stored in the `logs/` directory:
- Check `production_runner.log` for startup issues
- Check `grs_app.log` for application errors
- Check `gunicorn_*.log` for production server issues
