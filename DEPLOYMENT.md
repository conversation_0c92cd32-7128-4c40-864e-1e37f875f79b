# GRS Wall Designer - Deployment Guide

## Configuration System

The application now uses a centralized configuration system with two environments:

### Development Environment
- **File**: `config.py` - `DevelopmentConfig` class
- **Features**: Debug mode enabled, detailed logging, auto-reload
- **Database**: Local MySQL with default credentials
- **Security**: Relaxed cookie settings for development

### Production Environment  
- **File**: `config.py` - `ProductionConfig` class
- **Features**: Debug disabled, minimal logging, optimized for performance
- **Database**: Configurable via environment variables
- **Security**: Secure cookie settings, HTTPS ready

## Running the Application

### Development Mode
```bash
# Option 1: Direct execution
python run_development.py

# Option 2: Set environment and run app
set FLASK_ENV=development
python app.py

# Option 3: Using Flask CLI
set FLASK_ENV=development
flask run --host=0.0.0.0 --port=5000
```

### Production Mode
```bash
# Recommended: Use the production runner (with fallbacks)
python run_production.py

# Direct gunicorn command
gunicorn app:app --bind 0.0.0.0:5000 --workers 1 --timeout 120

# Set environment and run
set FLASK_ENV=production
python app.py
```

## Production Runner Features

The `run_production.py` script provides a robust deployment solution:

1. **Primary**: Attempts to start gunicorn production server
2. **Fallback 1**: If gunicorn fails, starts Flask development server
3. **Fallback 2**: If both fail, serves a maintenance page

### Production Server Command
```bash
gunicorn app:app --bind 0.0.0.0:5000 --workers 1 --timeout 120
```

## Environment Variables (Production)

Override default database settings in production:

```bash
set MYSQL_HOST=your-db-host
set MYSQL_USER=your-db-user  
set MYSQL_PASSWORD=your-db-password
set MYSQL_DB=your-db-name
set FLASK_ENV=production
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure MySQL is running and database exists

3. Run the application:
```bash
# Development
python run_development.py

# Production  
python run_production.py
```

## File Structure

```
├── config.py                 # Centralized configuration
├── app.py                    # Main Flask application
├── run_production.py         # Production server with fallbacks
├── run_development.py        # Development server
├── requirements.txt          # Python dependencies
├── templates/
│   └── maintenance.html      # Maintenance page template
└── logs/                     # Application logs
    ├── grs_app.log          # Main application log
    ├── production_runner.log # Production runner log
    ├── gunicorn_access.log  # Gunicorn access log
    └── gunicorn_error.log   # Gunicorn error log
```

## Configuration Details

### Database Configuration
- **Development**: Uses hardcoded local MySQL credentials
- **Production**: Uses environment variables with fallback to defaults

### Logging Configuration  
- **Development**: INFO level, console + file output
- **Production**: WARNING level, file output only

### Session Configuration
- **Development**: HTTP cookies allowed
- **Production**: HTTPS-only secure cookies

### Cache and Cleanup
- **Cache Timeout**: 30 seconds (configurable)
- **Cleanup Interval**: 60 seconds (configurable)

## Maintenance Page

If both production and development servers fail, a maintenance page is served with:
- Professional styling
- Auto-refresh every 30 seconds
- Contact information
- Status indicators

## Troubleshooting

### Gunicorn Not Found
```bash
pip install gunicorn
```

### Port Already in Use
The production runner checks port availability and will show appropriate errors.

### Database Connection Issues
Check MySQL service and credentials in config.py or environment variables.

### Logs Location
All logs are stored in the `logs/` directory:
- Check `production_runner.log` for startup issues
- Check `grs_app.log` for application errors
- Check `gunicorn_*.log` for production server issues
