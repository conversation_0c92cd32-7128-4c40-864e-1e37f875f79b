# GRS Wall Designer

A comprehensive web-based application for designing and analyzing Geosynthetic Reinforced Soil (GRS) retaining walls. This tool provides engineers with an intuitive interface to perform structural analysis, generate detailed reports, and visualize wall designs.

## Features

### 🏗️ **Design Capabilities**
- **Project Management**: Create and manage multiple wall design projects
- **Geometry Configuration**: Define wall dimensions, embedment depth, batter, and backslope
- **Soil Properties**: Configure retained soil, foundation soil, and backfill properties
- **Reinforcement Design**: Specify reinforcement properties and layout configurations
- **External Loads**: Apply dead loads, live loads, strip loads, and seismic forces

### 📊 **Analysis & Visualization**
- **Stability Analysis**: Internal and external stability calculations
- **Interactive Visualizations**: Real-time wall geometry and reinforcement layout displays
- **Screenshot Capture**: Save design visualizations for documentation
- **Dynamic Updates**: Live preview of changes without requiring saves

### 📋 **Reporting & Documentation**
- **PDF Reports**: Generate comprehensive analysis reports with calculations
- **Project Documentation**: Maintain project information and revision history
- **Data Persistence**: Automatic saving and restoration of design data

### 👥 **User Management**
- **Multi-user Support**: Individual user accounts with isolated data
- **Admin Interface**: User management and access control
- **Session Management**: Secure authentication and session handling
- **Privacy Controls**: Built-in privacy policy management

## Technology Stack

- **Backend**: Python Flask framework
- **Database**: MySQL with Flask-MySQLdb
- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **PDF Generation**: WeasyPrint
- **Production Server**: Gunicorn (Linux)
- **Visualization**: Custom SVG-based drawing engine

## Installation

### Prerequisites
- Python 3.8+
- MySQL Server
- Linux environment (for production deployment)

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd GRS_Software
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure MySQL Database**
   - Ensure MySQL server is running
   - Create database: `grs_software`
   - Update credentials in `config.py` if needed (default: localhost, root, sriroot)

4. **Initialize the application**
   ```bash
   python app.py
   ```
   The application will automatically create database tables and a default admin user.

## Usage

### Development Mode
```bash
python app.py
```
- Runs on `http://localhost:5000`
- Debug mode enabled
- Detailed logging (INFO level)
- Auto-reload on code changes

### Production Mode
```bash
python run_production.py
```
- Automatic environment configuration
- Gunicorn production server with fallbacks
- Critical-only logging
- Optimized for performance

## Configuration

The application uses a centralized configuration system in `config.py`:

### Development Environment
- Debug mode enabled
- INFO level logging (console + file)
- Relaxed security settings
- Auto-reload enabled

### Production Environment
- Debug mode disabled
- CRITICAL level logging (file only)
- Secure cookie settings
- Performance optimized

### Database Configuration
Both environments use the same MySQL configuration:
- **Host**: localhost
- **User**: root
- **Password**: sriroot
- **Database**: grs_software

## Project Structure

```
GRS_Software/
├── app.py                    # Main Flask application
├── backend.py                # Analysis calculations and backend logic
├── config.py                 # Centralized configuration management
├── run_production.py         # Production server with fallback mechanisms
├── requirements.txt          # Python dependencies
├── static/                   # Static assets (CSS, JS, images)
│   ├── css/                 # Stylesheets
│   └── js/                  # JavaScript files
├── templates/                # HTML templates
│   ├── base.html            # Base template
│   ├── home.html            # Dashboard
│   ├── geometry.html        # Geometry configuration
│   ├── reinforcementlayout.html # Reinforcement design
│   ├── run_analysis.html    # Analysis results
│   ├── report.html          # PDF report template
│   ├── maintenance.html     # Maintenance page
│   └── ...                  # Other section templates
└── logs/                     # Application logs
    ├── grs_app.log          # Main application log
    ├── grs_summary.log      # Summary events log
    └── production_runner.log # Production server log
```

## Key Features Explained

### Design Workflow
1. **Project Information**: Set up project details, engineer information, and revision tracking
2. **Geometry**: Define wall dimensions, embedment depth, batter angle, and backslope
3. **Soil Properties**: Configure soil parameters for retained, foundation, and backfill materials
4. **External Loads**: Apply various loading conditions including seismic forces
5. **Reinforcement Properties**: Define reinforcement material properties and types
6. **Reinforcement Layout**: Design the reinforcement layout with length and spacing
7. **Analysis**: Run stability calculations and generate results
8. **Reporting**: Generate comprehensive PDF reports with all calculations

### Navigation System
- **AJAX-based Navigation**: Seamless transitions between sections without page reloads
- **Data Persistence**: Form data is automatically saved and restored across sessions
- **Progress Indicators**: Visual feedback showing completion status of each section
- **Validation**: Real-time form validation with helpful error messages

### Visualization Engine
- **Interactive SVG Graphics**: Dynamic wall and reinforcement visualizations
- **Real-time Updates**: Visualizations update as you modify parameters
- **Screenshot Capability**: Capture and save design visualizations
- **Responsive Design**: Works across different screen sizes and devices

## Production Deployment

### Server Requirements
- **Operating System**: Linux (Ubuntu 18.04+ recommended)
- **Python**: 3.8 or higher
- **MySQL**: 5.7 or higher
- **Memory**: Minimum 2GB RAM
- **Storage**: 10GB available space

### Production Features
- **Gunicorn WSGI Server**: High-performance production server
- **Automatic Fallbacks**: Falls back to development server if gunicorn fails
- **Maintenance Mode**: Displays maintenance page if all servers fail
- **Logging**: Critical-only logging for performance
- **Security**: Secure cookie settings and HTTPS-ready configuration

### Monitoring & Logs
All logs are stored in the `logs/` directory:
- **grs_app.log**: Main application events and errors
- **grs_summary.log**: Critical system events and user actions
- **production_runner.log**: Server startup and deployment events
- **gunicorn_access.log**: HTTP access logs (production only)
- **gunicorn_error.log**: Server error logs (production only)

## Default Credentials

### Admin Account
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>

⚠️ **Important**: Change the default admin password immediately after first login.

## API Endpoints

The application provides several AJAX endpoints for dynamic functionality:

### Data Management
- `POST /project_info` - Save project information
- `POST /geometry` - Save geometry configuration
- `POST /reinforcementproperties` - Save reinforcement properties
- `POST /reinforcementlayout` - Save reinforcement layout
- `POST /externalloads` - Save external loads data

### Analysis & Reporting
- `POST /run_analysis` - Execute stability analysis
- `GET /report` - Generate PDF report
- `POST /take_screenshot` - Capture visualization screenshots

### User Management
- `POST /login` - User authentication
- `POST /logout` - Session termination
- `GET /admin/*` - Admin interface endpoints

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly in both development and production modes
5. Submit a pull request

### Code Style
- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add comments for complex logic
- Maintain consistent indentation (4 spaces)

### Testing
- Test all functionality in both development and production modes
- Verify database operations work correctly
- Check that visualizations render properly
- Ensure PDF generation works as expected

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For technical support or questions:
- **Email**: <EMAIL>
- **Issues**: Use the GitHub issue tracker for bug reports and feature requests

## Acknowledgments

- Built with Flask web framework
- Uses WeasyPrint for PDF generation
- Visualization powered by custom SVG engine
- Database management with MySQL
- Production deployment with Gunicorn