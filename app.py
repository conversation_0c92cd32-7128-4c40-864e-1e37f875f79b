# ============================================================================
# IMPORTS
# ============================================================================
import time
import uuid
import base64
import logging
import random
import string
import math
import os
from datetime import datetime
from contextlib import contextmanager
from collections import defaultdict
from functools import wraps

from flask import (
    Flask, render_template, request, jsonify, redirect, url_for, 
    session, flash, Response, make_response, g
)
from flask_mysqldb import MySQL
from weasyprint import HTML

from backend import calculate_pressure
from backend import safe_float_conversion

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================
# Suppress only fontTools debug logs
logging.getLogger('fontTools').setLevel(logging.WARNING)
logging.getLogger('fontTools.subset').setLevel(logging.WARNING)
logging.getLogger('fontTools.subset.timer').setLevel(logging.WARNING)
logging.getLogger('fontTools.ttLib').setLevel(logging.WARNING)

# Ensure logs directory exists
os.makedirs('logs', exist_ok=True)

# Configure application logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/grs_app.log'),
        logging.StreamHandler()
    ]
)

# Create logger
app_logger = logging.getLogger('grs_app')

# Summary logger for critical events
summary_logger = logging.getLogger('grs_summary')
if not summary_logger.handlers:
    summary_handler = logging.FileHandler('logs/grs_summary.log')
    summary_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    summary_handler.setFormatter(summary_formatter)
    summary_logger.addHandler(summary_handler)
    summary_logger.setLevel(logging.INFO)

# Track username usage for duplicate handling
username_count = defaultdict(int)

def get_unique_log_filename(username):
    """Get unique log filename, handling duplicates with _1, _2, etc."""
    global username_count
    
    # Sanitize username for filename
    safe_username = "".join(c if c.isalnum() or c in '_-' else '_' for c in username)
    
    if username_count[safe_username] == 0:
        # First time this username is used
        username_count[safe_username] = 1
        return f'logs/{safe_username}.log'
    else:
        # Username already exists, increment counter
        username_count[safe_username] += 1
        return f'logs/{safe_username}_{username_count[safe_username] - 1}.log'

def get_user_logger(user_id=None, username=None):
    """Get user-specific logger with username-based filenames"""
    # Get username from database if only user_id provided
    if user_id and not username:
        username = get_username_by_id(user_id)
    
    # Use provided username or fallback
    if not username:
        username = "anonymous"
    
    logger_name = f'grs_app.{username}'
    user_logger = logging.getLogger(logger_name)
    
    if not user_logger.handlers:
        # Create user-specific log file handler with unique filename
        log_filename = get_unique_log_filename(username)
        handler = logging.FileHandler(log_filename)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        user_logger.addHandler(handler)
        user_logger.setLevel(logging.INFO)
        user_logger.propagate = False  # Prevent duplicate logging to root logger
        
        # Log initial setup
        user_logger.info(f"Logger initialized for user: {username}")
    
    return user_logger

def log_critical_event(event_type, username, details, user_id=None):
    """Log critical events to summary file"""
    summary_logger.warning(f"CRITICAL EVENT - {event_type}: User={username}, UserID={user_id}, Details={details}")
    
def log_login_attempt(username, success=True, ip_address="unknown", details=""):
    """Log login attempts to summary file"""
    status = "SUCCESS" if success else "FAILED"
    summary_logger.info(f"LOGIN {status}: User={username}, IP={ip_address}, Details={details}")

def log_logout_attempt(username, user_id=None, reason="user_initiated", ip_address="unknown"):
    """Log logout attempts to summary file"""
    summary_logger.info(f"LOGOUT: User={username}, UserID={user_id}, Reason={reason}, IP={ip_address}")

# ============================================================================
# APPLICATION SETUP
# ============================================================================
app = Flask(__name__)

# Configure Flask
app.secret_key = 'YourSecretKeyHere'  # IMPORTANT: Replace with a real secret key in production
# Use Flask's built-in sessions instead of Flask-Session filesystem storage
# This prevents automatic session file creation at startup

# Configure MySQL Database
app.config.update({
    'MYSQL_HOST': 'localhost',
    'MYSQL_USER': 'root',
    'MYSQL_PASSWORD': 'sriroot',
    'MYSQL_DB': 'grs_software',
    'MYSQL_POOL_SIZE': 10,
    'MYSQL_POOL_TIMEOUT': 20,
    'MYSQL_POOL_RECYCLE': 3600,
    'MYSQL_AUTOCOMMIT': True
})

# Initialize extensions
mysql = MySQL(app)

# ============================================================================
# GLOBAL VARIABLES
# ============================================================================
# Session validation cache
session_validation_cache = {}
CACHE_TIMEOUT = 30  # Reduced to 30 seconds for faster invalidation

# Dictionary to store screenshots in memory (user-isolated)
layout_screenshots = {}

# User-specific data storage (user-isolated)
user_data_store = {}

# Session cleanup tracking
last_cleanup_time = 0
CLEANUP_INTERVAL = 60  # Clean up expired sessions every 60 seconds

# ============================================================================
# USER DATA ISOLATION FUNCTIONS
# ============================================================================
def get_user_data_key(user_id, data_type):
    """Generate user-specific data key"""
    return f"user_{user_id}_{data_type}"

def get_user_data(user_id, data_type, default=None):
    """Get user-specific data"""
    key = get_user_data_key(user_id, data_type)
    return user_data_store.get(key, default)

def set_user_data(user_id, data_type, data):
    """Set user-specific data"""
    key = get_user_data_key(user_id, data_type)
    user_data_store[key] = data
    
    # Log data change - Use consistent logging pattern
    try:
        # Try to get username from database
        with get_db_cursor() as cur:
            cur.execute("SELECT username FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            username = result[0] if result else f"user_{user_id}"
    except Exception:
        username = f"user_{user_id}"
    
    user_logger = get_user_logger(user_id, username)
    user_logger.info(f"Data updated: {data_type}")

def clear_user_data(user_id, data_type=None):
    """Clear user-specific data"""
    if data_type:
        key = get_user_data_key(user_id, data_type)
        if key in user_data_store:
            del user_data_store[key]
    else:
        # Clear all data for user
        keys_to_remove = [k for k in user_data_store.keys() if k.startswith(f"user_{user_id}_")]
        for key in keys_to_remove:
            del user_data_store[key]
    
    # Log data clear - Use consistent logging pattern
    username = get_username_by_id(user_id)
    
    user_logger = get_user_logger(user_id, username)
    user_logger.info(f"Data cleared: {data_type if data_type else 'all'}")

def get_user_screenshot_key(user_id, screenshot_id):
    """Generate user-specific screenshot key"""
    return f"user_{user_id}_{screenshot_id}"

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================
@contextmanager
def get_db_cursor():
    """Database cursor context manager for automatic cleanup"""
    cur = None
    try:
        cur = mysql.connection.cursor()
        yield cur
        mysql.connection.commit()
    except Exception as e:
        if mysql.connection:
            mysql.connection.rollback()
        raise e
    finally:
        if cur:
            cur.close()

def get_username_by_id(user_id):
    """Helper function to get username by user_id"""
    try:
        with get_db_cursor() as cur:
            cur.execute("SELECT username FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            return result[0] if result else f"user_{user_id}"
    except Exception as e:
        app_logger.error(f"Error getting username for user_id {user_id}: {e}")
        return f"user_{user_id}"

def add_padding(base64_string):
    """Add padding to base64 string if needed."""
    return base64_string + '=' * (-len(base64_string) % 4)

def get_save_message(section_name):
    """Get appropriate save message based on whether analysis has been run"""
    base_message = f'{section_name} saved successfully!'
    if session.get('analysis_has_been_run', False):
        return f'{base_message} Please rerun the analysis to see updated results.'
    else:
        return base_message

def clear_layout_screenshot(reason="data change"):
    """Clear existing layout screenshot since data has changed"""
    user_id = session.get('user_id')
    if not user_id:
        return False
    
    # Get username for consistent logging
    username = session.get('user')
    if not username:
        username = get_username_by_id(user_id)
        
    old_screenshot_id = session.get('current_screenshot_id')
    if old_screenshot_id:
        user_screenshot_key = get_user_screenshot_key(user_id, old_screenshot_id)
        if user_screenshot_key in layout_screenshots:
            del layout_screenshots[user_screenshot_key]
            session.pop('current_screenshot_id', None)
            
            # Log screenshot clear with consistent pattern
            user_logger = get_user_logger(user_id, username)
            user_logger.info(f"Screenshot cleared: {reason}")
            return True
    return False

def generate_wall_diagram_svg(user_data):
    """Generate wall diagram SVG server-side using user data - matches frontend exactly"""
    try:
        # Extract data from user_data
        geometry_data = user_data.get('geometryData', {})
        external_loads = user_data.get('externalloads_data', {})
        reinforcement_layout = user_data.get('reinforcement_layout_data', [])
        
        # Get geometry parameters with defaults (matching frontend)
        try:
            wall_height = float(geometry_data.get('wallHeight', 5))
        except (ValueError, TypeError):
            wall_height = 5.0
            
        try:
            wall_length = float(geometry_data.get('wallLength', 6))
        except (ValueError, TypeError):
            wall_length = 6.0
            
        try:
            wall_batter = float(geometry_data.get('wallBatter', 0))
        except (ValueError, TypeError):
            wall_batter = 0.0
            
        try:
            embedment_depth = float(geometry_data.get('embedmentDepth', 1))
        except (ValueError, TypeError):
            embedment_depth = 1.0
            
        try:
            backslope_angle = float(geometry_data.get('backslopeAngle', 0))
        except (ValueError, TypeError):
            backslope_angle = 0.0
            
        try:
            backslope_rise = float(geometry_data.get('backslopeRise', 2))
        except (ValueError, TypeError):
            backslope_rise = 2.0
        
        # Extract external loads data (matching frontend)
        dead_loads = external_loads.get('dead_loads', [0, 0, 0])
        live_loads = external_loads.get('live_loads', [0, 0, 0])
        vertical_strip_load = external_loads.get('vertical_strip_load', [0, 0, 0])
        horizontal_strip_load = external_loads.get('horizontal_strip_load', [0, 0, 0])
        strip_load_width = external_loads.get('strip_load_width', [0, 0, 0])
        strip_load_distance = external_loads.get('strip_load_distance', [0, 0, 0])
        
        try:
            earthquake_acceleration = float(external_loads.get('earthquake_acceleration', 0))
        except (ValueError, TypeError):
            earthquake_acceleration = 0.0
        
        # Canvas dimensions (matching frontend)
        canvas_width = 1000
        canvas_height = 600
        
        # Scale and positioning (exact match with frontend)
        base_scale = 50
        base_x = 100
        base_y = 400
        
        # Calculate geometry (exact match with frontend)
        batter_offset = math.tan(math.radians(wall_batter)) * (wall_height * base_scale)
        fascia_thickness = 0.2 * base_scale
        
        # Define geometry objects (exact match with frontend)
        fascia = {
            'x1': base_x - fascia_thickness,
            'y1': base_y,
            'x2': base_x - fascia_thickness + batter_offset,
            'y2': base_y - (wall_height * base_scale),
            'x3': base_x + batter_offset + fascia_thickness,
            'y3': base_y - (wall_height * base_scale),
            'x4': base_x,
            'y4': base_y
        }
        
        reinforced_fill = {
            'x1': base_x,
            'y1': base_y,
            'x2': base_x + batter_offset,
            'y2': base_y - (wall_height * base_scale),
            'x3': base_x + (wall_length * base_scale) + batter_offset,
            'y3': base_y - (wall_height * base_scale),
            'x4': base_x + (wall_length * base_scale),
            'y4': base_y
        }
        
        retained_fill = {
            'x1': base_x + (wall_length * base_scale),
            'y1': base_y,
            'x2': base_x + (wall_length * base_scale) + batter_offset,
            'y2': base_y - (wall_height * base_scale),
            'x3': base_x + (wall_length * base_scale) + (2 * wall_height * base_scale),
            'y3': base_y - (wall_height * base_scale),
            'x4': base_x + (wall_length * base_scale) + (2 * wall_height * base_scale),
            'y4': base_y
        }
        
        embedment = {
            'x1': base_x - 10 - (1 * wall_height * base_scale),
            'y1': base_y,
            'x2': base_x - 10 + batter_offset,
            'y2': base_y - (embedment_depth * base_scale)
        }
        
        foundation_soil = {
            'x1': embedment['x1'],
            'y1': base_y + (0.5 * wall_height * base_scale),
            'x2': retained_fill['x4'],
            'y2': base_y
        }
        
        # Calculate backslope (exact match with frontend)
        backslope_angle_radians = math.radians(backslope_angle)
        slope_base_length = backslope_rise / math.tan(backslope_angle_radians) if backslope_angle > 0 else 0
        slope_start_x = reinforced_fill['x2']
        slope_start_y = reinforced_fill['y2']
        slope_end_x = slope_start_x + (slope_base_length * base_scale)
        slope_end_y = slope_start_y - (backslope_rise * base_scale)
        
        # Check if Case 1 (slope fits within retained fill)
        is_case1 = slope_end_x <= retained_fill['x3'] if backslope_angle > 0 else True
        
        if not is_case1:
            slope_end_x = retained_fill['x3']
            slope_end_y = slope_start_y - ((slope_end_x - slope_start_x) * math.tan(backslope_angle_radians))
        
        # Start building SVG with exact frontend colors
        svg_content = f'''<svg width="{canvas_width}" height="{canvas_height}" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                        refX="0" refY="3.5" orient="auto">
                    <polygon points="0 0, 10 3.5, 0 7" fill="#000"/>
                </marker>
            </defs>
            
            <!-- Background -->
            <rect width="{canvas_width}" height="{canvas_height}" fill="#f8f9fa"/>'''
        
        # Draw backslope first (if exists)
        if backslope_angle > 0:
            if is_case1:
                horizontal_end_x = retained_fill['x3']
                horizontal_end_y = slope_end_y
                svg_content += f'''
                <!-- Backslope -->
                <polygon points="{slope_start_x},{slope_start_y} {slope_end_x},{slope_end_y} 
                               {horizontal_end_x},{horizontal_end_y} {retained_fill['x3']},{retained_fill['y3']}" 
                         fill="#FFA500" stroke="#000" stroke-width="1"/>
                <line x1="{slope_start_x}" y1="{slope_start_y}" x2="{slope_end_x}" y2="{slope_end_y}" stroke="#000" stroke-width="1"/>
                <line x1="{slope_end_x}" y1="{slope_end_y}" x2="{horizontal_end_x}" y2="{horizontal_end_y}" stroke="#000" stroke-width="1"/>
                
                <!-- Backslope rise dimension -->
                <line x1="{retained_fill['x3']}" y1="{retained_fill['y3']}" x2="{retained_fill['x3']}" y2="{retained_fill['y3'] - backslope_rise * base_scale}" 
                      stroke="#000" stroke-width="1" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
                <text x="{retained_fill['x3'] - 40}" y="{retained_fill['y3'] - (backslope_rise * base_scale / 2)}" 
                      font-family="Arial" font-size="18" fill="#000" text-anchor="center">
                    {backslope_rise:.2f}m
                </text>'''
            else:
                top_rise = (retained_fill['x3'] - reinforced_fill['x2']) * math.tan(backslope_angle_radians) / base_scale
                svg_content += f'''
                <!-- Backslope (Case 2) -->
                <polygon points="{slope_start_x},{slope_start_y} {slope_end_x},{slope_end_y} {retained_fill['x3']},{retained_fill['y3']}" 
                         fill="#FFA500" stroke="#000" stroke-width="1"/>
                <line x1="{slope_start_x}" y1="{slope_start_y}" x2="{slope_end_x}" y2="{slope_end_y}" stroke="#000" stroke-width="1"/>
                
                <!-- Backslope rise dimension -->
                <line x1="{retained_fill['x3']}" y1="{retained_fill['y3']}" x2="{retained_fill['x3']}" y2="{retained_fill['y3'] - top_rise * base_scale}" 
                      stroke="#000" stroke-width="1" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
                <text x="{retained_fill['x3'] - 40}" y="{retained_fill['y3'] - (top_rise * base_scale * 0.5)}" 
                      font-family="Arial" font-size="18" fill="#000" text-anchor="center">
                    {top_rise:.2f}m
                </text>'''
        else:
            # No backslope case
            svg_content += f'''
            <!-- No backslope dimension -->
            <line x1="{retained_fill['x3']}" y1="{retained_fill['y3']}" x2="{retained_fill['x3']}" y2="{retained_fill['y3']}" 
                  stroke="#000" stroke-width="1" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
            <text x="{retained_fill['x3'] - 40}" y="{retained_fill['y3']}" 
                  font-family="Arial" font-size="18" fill="#000" text-anchor="center">
                0.00m
            </text>'''
        
        # Draw foundation soil
        svg_content += f'''
        <!-- Foundation soil -->
        <rect x="{foundation_soil['x1']}" y="{foundation_soil['y1']}" 
              width="{foundation_soil['x2'] - foundation_soil['x1']}" 
              height="{foundation_soil['y2'] - foundation_soil['y1']}" 
              fill="#A98B6D"/>'''
        
        # Draw embedment
        svg_content += f'''
        <!-- Embedment -->
        <rect x="{embedment['x1']}" y="{embedment['y1']}" 
              width="{embedment['x2'] - embedment['x1']}" 
              height="{embedment['y2'] - embedment['y1']}" 
              fill="#A98B6D"/>'''
        
        # Draw fascia (exact frontend colors)
        svg_content += f'''
        <!-- Fascia -->
        <polygon points="{fascia['x1']},{fascia['y1']} {fascia['x2']},{fascia['y2']} 
                       {fascia['x3']},{fascia['y3']} {fascia['x4']},{fascia['y4']}" 
                 fill="#666"/>'''
        
        # Draw reinforced fill (exact frontend colors)
        svg_content += f'''
        <!-- Reinforced fill -->
        <polygon points="{reinforced_fill['x1']},{reinforced_fill['y1']} {reinforced_fill['x2']},{reinforced_fill['y2']} 
                       {reinforced_fill['x3']},{reinforced_fill['y3']} {reinforced_fill['x4']},{reinforced_fill['y4']}" 
                 fill="#D6B85A"/>'''
        
        # Draw retained fill (exact frontend colors)
        svg_content += f'''
        <!-- Retained fill -->
        <polygon points="{retained_fill['x1']},{retained_fill['y1']} {retained_fill['x2']},{retained_fill['y2']} 
                       {retained_fill['x3']},{retained_fill['y3']} {retained_fill['x4']},{retained_fill['y4']}" 
                 fill="#D2B48C"/>'''
        
        # Draw dimensions (exact match with frontend)
        svg_content += f'''
        <!-- Wall height dimension -->
        <line x1="{base_x - 20}" y1="{base_y}" x2="{base_x - 20}" y2="{base_y - (wall_height * base_scale)}" 
              stroke="#000" stroke-width="1" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
        <text x="{base_x - 40}" y="{base_y - (0.5 * wall_height * base_scale) - 10}" 
              font-family="Arial" font-size="18" fill="#000" text-anchor="center">
            {wall_height}m
        </text>
        
        <!-- Wall length dimension -->
        <line x1="{base_x}" y1="{base_y}" x2="{base_x + (wall_length * base_scale)}" y2="{base_y}" 
              stroke="#000" stroke-width="1" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
        <text x="{base_x + ((wall_length * base_scale) / 2)}" y="{base_y + 10}" 
              font-family="Arial" font-size="18" fill="#000" text-anchor="center">
            {wall_length}m
        </text>
        
        <!-- Embedment depth dimension -->
        <line x1="{base_x - 40}" y1="{base_y}" x2="{base_x - 40}" y2="{base_y - (embedment_depth * base_scale)}" 
              stroke="#000" stroke-width="1" marker-start="url(#arrowhead)" marker-end="url(#arrowhead)"/>
        <text x="{base_x - 50 - fascia_thickness}" y="{base_y - (0.5 * embedment_depth * base_scale) - 10}" 
              font-family="Arial" font-size="18" fill="#000" text-anchor="center">
            {embedment_depth}m
        </text>'''
        
        # Draw labels (exact match with frontend)
        svg_content += f'''
        <!-- Labels -->
        <text x="{base_x + ((0.5 * wall_length * base_scale))}" y="{base_y - (0.5 * wall_height * base_scale) - 10}" 
              font-family="Arial" font-size="18" fill="#000" text-anchor="center">
            Reinforced Fill
        </text>
        <text x="{base_x + ((1.5 * wall_length * base_scale))}" y="{base_y - (0.5 * wall_height * base_scale) - 10}" 
              font-family="Arial" font-size="18" fill="#000" text-anchor="center">
            Retained Fill
        </text>
        <text x="{base_x + ((1.0 * wall_length * base_scale))}" y="{base_y + (0.25 * wall_height * base_scale) - 10}" 
              font-family="Arial" font-size="18" fill="#000" text-anchor="center">
            Foundation Soil
        </text>'''
        
        # Draw loads (exact match with frontend)
        dead_load_sum = sum(dead_loads)
        live_load_sum = sum(live_loads)
        
        if backslope_angle == 0:
            load_y = reinforced_fill['y2'] - 50
            # Draw load arrows every 50 pixels
            for x in range(int(reinforced_fill['x2']), int(retained_fill['x3']) + 1, 50):
                svg_content += f'''
                <line x1="{x}" y1="{load_y}" x2="{x}" y2="{load_y + 20}" stroke="red" stroke-width="2"/>
                <polygon points="{x},{load_y + 20} {x-5},{load_y + 15} {x+5},{load_y + 15}" fill="red"/>'''
            
            svg_content += f'''
            <text x="{(reinforced_fill['x2'] + retained_fill['x3']) / 2}" y="{load_y - 25}" 
                  font-family="Arial" font-size="18" fill="red" text-anchor="center">
                Dead Load: {dead_load_sum:.2f}kpa
            </text>'''
            
            load_y2 = load_y - 50
            for x in range(int(reinforced_fill['x2']), int(retained_fill['x3']) + 1, 50):
                svg_content += f'''
                <line x1="{x}" y1="{load_y2}" x2="{x}" y2="{load_y2 + 20}" stroke="blue" stroke-width="2"/>
                <polygon points="{x},{load_y2 + 20} {x-5},{load_y2 + 15} {x+5},{load_y2 + 15}" fill="blue"/>'''
            
            svg_content += f'''
            <text x="{(reinforced_fill['x2'] + retained_fill['x3']) / 2}" y="{load_y2 - 25}" 
                  font-family="Arial" font-size="18" fill="blue" text-anchor="center">
                Live Load: {live_load_sum:.2f}kpa
            </text>'''
        elif is_case1:
            load_y = slope_end_y - 50
            for x in range(int(slope_end_x), int(retained_fill['x3']) + 1, 50):
                svg_content += f'''
                <line x1="{x}" y1="{load_y}" x2="{x}" y2="{load_y + 20}" stroke="red" stroke-width="2"/>
                <polygon points="{x},{load_y + 20} {x-5},{load_y + 15} {x+5},{load_y + 15}" fill="red"/>'''
            
            svg_content += f'''
            <text x="{(slope_end_x + retained_fill['x3']) / 2}" y="{load_y - 25}" 
                  font-family="Arial" font-size="18" fill="red" text-anchor="center">
                Dead Load: {dead_load_sum:.2f}kpa
            </text>'''
        else:
            svg_content += f'''
            <text x="{(reinforced_fill['x2'] + retained_fill['x3']) / 2}" y="{reinforced_fill['y2'] - 50}" 
                  font-family="Arial" font-size="18" fill="#000" text-anchor="center">
                Dead and Live Loads: Beyond Influence Zone
            </text>'''
        
        # Draw reinforcement layers (exact match with frontend)
        if reinforcement_layout and isinstance(reinforcement_layout, list):
            grade_colors = {}
            def get_random_color():
                return f"rgb({random.randint(0, 255)},{random.randint(0, 255)},{random.randint(0, 255)})"
            
            for data in reinforcement_layout:
                if isinstance(data, dict):
                    try:
                        location = float(data.get('location', 0))
                        length = float(data.get('length', 0))
                        reinforcement_type = str(data.get('reinforcement_type', ''))
                    except (ValueError, TypeError):
                        continue  # Skip this layer if conversion fails
                    
                    if location > 0 and length > 0 and reinforcement_type:
                        location_from_bottom = location * base_scale
                        length_of_reinforcement = length * base_scale
                        
                        if reinforcement_type not in grade_colors:
                            grade_colors[reinforcement_type] = get_random_color()
                        
                        color = grade_colors[reinforcement_type]
                        y_pos = reinforced_fill['y1'] - location_from_bottom
                        start_x = reinforced_fill['x1'] + math.tan(math.radians(wall_batter)) * location_from_bottom
                        end_x = start_x + length_of_reinforcement
                        
                        svg_content += f'''
                        <!-- Reinforcement layer -->
                        <line x1="{start_x}" y1="{y_pos}" x2="{end_x}" y2="{y_pos}" 
                              stroke="{color}" stroke-width="2"/>
                        <text x="{start_x + length_of_reinforcement / 2 - 30}" y="{y_pos - 10}" 
                              font-family="Arial" font-size="12" fill="{color}" text-anchor="middle">
                            L = {(length_of_reinforcement / base_scale):.2f}m,{reinforcement_type}
                        </text>'''
        
        # Draw earthquake acceleration (exact match with frontend)
        if earthquake_acceleration > 0:
            eq_x = (retained_fill['x1'] + retained_fill['x3']) / 2
            eq_y = (retained_fill['y1'] + retained_fill['y3'] + 60) / 2
            arrow_length = 50
            arrow_size = 10
            
            svg_content += f'''
            <!-- Earthquake acceleration -->
            <line x1="{eq_x}" y1="{eq_y}" x2="{eq_x - arrow_length}" y2="{eq_y}" stroke="#000" stroke-width="2"/>
            <polygon points="{eq_x - arrow_length},{eq_y} {eq_x - arrow_length + arrow_size},{eq_y - arrow_size/2} {eq_x - arrow_length + arrow_size},{eq_y + arrow_size/2}" fill="#000"/>
            <text x="{eq_x - arrow_length - 30}" y="{eq_y}" 
                  font-family="Arial" font-size="18" fill="#000" text-anchor="center">
                {earthquake_acceleration:.2f}g
            </text>'''
        
        svg_content += '</svg>'
        
        return svg_content
        
    except Exception as e:
        # Return a simple error diagram
        return f'''<svg width="1000" height="600" xmlns="http://www.w3.org/2000/svg">
            <rect width="1000" height="600" fill="#f8f9fa"/>
            <text x="500" y="300" text-anchor="middle" font-family="Arial" font-size="16" fill="#dc3545">
                Error generating diagram: {str(e)}
            </text>
        </svg>'''

def has_required_data_for_visualization():
    """Check if we have all required data to generate a visualization"""
    user_id = session.get('user_id')
    if not user_id:
        return False
        
    required_fields = [
        'geometryData', 'soil_density', 'friction_angle', 'cohesion',
        'retainedsoil_density', 'retainedfriction_angle', 'retainedcohesion',
        'foundationsoildensity', 'foundationsoilfriction_angle', 'foundationsoilcohesion','eccentricity', 'eccentricityseismic',
        'reinforcement_data', 'reinforcement_layout_data'
    ]
    
    for field in required_fields:
        if not get_user_data(user_id, field):
            return False
    
    # Check if geometry data has the required fields
    geometry_data = get_user_data(user_id, 'geometryData', {})
    required_geometry_fields = ['wallHeight', 'embedmentDepth', 'wallLength', 'wallBatter', 'backslopeAngle', 'backslopeRise']
    for field in required_geometry_fields:
        if not geometry_data.get(field):
            return False
    
    return True

# ============================================================================
# SESSION VALIDATION FUNCTIONS
# ============================================================================
def get_cached_session_validation(session_id, user_id):
    """Get cached session validation result"""
    cache_key = f"{session_id}:{user_id}"
    if cache_key in session_validation_cache:
        cached_time, result = session_validation_cache[cache_key]
        if time.time() - cached_time < CACHE_TIMEOUT:
            return result
    return None

def cache_session_validation(session_id, user_id, result):
    """Cache session validation result"""
    cache_key = f"{session_id}:{user_id}"
    session_validation_cache[cache_key] = (time.time(), result)
    
    # Clean old cache entries
    current_time = time.time()
    expired_keys = [k for k, (t, _) in session_validation_cache.items()
                   if current_time - t > CACHE_TIMEOUT]
    for key in expired_keys:
        del session_validation_cache[key]

def clear_session_validation_cache(user_id=None, session_id=None):
    """Clear session validation cache for specific user or session"""
    if user_id:
        # Clear all cache entries for this user
        keys_to_remove = [key for key in session_validation_cache.keys() 
                         if key.endswith(f":{user_id}")]
        for key in keys_to_remove:
            del session_validation_cache[key]
    elif session_id:
        # Clear cache entry for this specific session
        keys_to_remove = [key for key in session_validation_cache.keys() 
                         if key.startswith(f"{session_id}:")]
        for key in keys_to_remove:
            del session_validation_cache[key]
    else:
        # Clear all cache
        session_validation_cache.clear()

# ============================================================================
# SESSION MANAGEMENT FUNCTIONS
# ============================================================================
def create_user_session(user_id):
    """Create a new session for the user and invalidate any existing sessions"""
    try:
        with get_db_cursor() as cur:
            # Get username for logging
            username = get_username_by_id(user_id)
            
            # Log session creation attempt
            user_logger = get_user_logger(user_id, username)
            user_logger.info(f"Creating new session for user: {username}")
            
            # Clear session validation cache for this user before deleting sessions
            clear_session_validation_cache(user_id=user_id)
            
            # Clear any existing user data
            clear_user_data(user_id)
            
            cur.execute("DELETE FROM user_sessions WHERE user_id = %s", (user_id,))
            
            session_id = str(uuid.uuid4())
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
            user_agent = request.environ.get('HTTP_USER_AGENT', '')
            
            cur.execute("""
                INSERT INTO user_sessions (user_id, session_id, ip_address, user_agent)
                VALUES (%s, %s, %s, %s)
            """, (user_id, session_id, ip_address, user_agent))
            
            session['session_id'] = session_id
            session['user_id'] = user_id
            
            user_logger.info(f"Session created successfully: {session_id[:8]}... from IP: {ip_address}")
            return session_id
    except Exception as e:
        if user_id:
            user_logger = get_user_logger(user_id)
            user_logger.error(f"Failed to create session: {str(e)}")
        else:
            app_logger.error(f"Failed to create session for user {user_id}: {str(e)}")
        return None

def validate_user_session(use_cache=True):
    """Validate if the current session is still active in the database with optional caching"""
    session_id = session.get('session_id')
    user_id = session.get('user_id')
    
    if not session_id or not user_id:
        return False
    
    # Check cache first only if use_cache is True
    if use_cache:
        cached_result = get_cached_session_validation(session_id, user_id)
        if cached_result is not None:
            return cached_result
    
    try:
        with get_db_cursor() as cur:
            # Check if session exists and is not expired (within 30 minutes of last activity)
            cur.execute("""
                SELECT us.id, u.username, u.role
                FROM user_sessions us
                JOIN users u ON us.user_id = u.id
                WHERE us.session_id = %s AND us.user_id = %s
                AND us.last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            """, (session_id, user_id))
            
            result = cur.fetchone()
            if result:
                # Session is valid and not expired, update last activity
                cur.execute("UPDATE user_sessions SET last_activity = NOW() WHERE session_id = %s", (session_id,))
                if use_cache:
                    cache_session_validation(session_id, user_id, True)
                return True
            else:
                # Session is either expired or doesn't exist, clean up
                cur.execute("DELETE FROM user_sessions WHERE session_id = %s", (session_id,))
                clear_all_session_data()
                if use_cache:
                    cache_session_validation(session_id, user_id, False)
                return False
    except Exception as e:
        app_logger.error(f"Error validating session: {e}")
        return False

def cleanup_user_session(user_id=None, session_id=None):
    """Clean up user session from database"""
    username = None
    try:
        # Get username for logging if user_id provided
        if user_id:
            username = get_username_by_id(user_id)
        
        # Clear session validation cache first
        if user_id:
            clear_session_validation_cache(user_id=user_id)
            # Clear user-specific data
            clear_user_data(user_id)
            # Log cleanup
            if username:
                user_logger = get_user_logger(user_id, username)
                user_logger.info("Session and data cleanup initiated")
                log_logout_attempt(username, user_id, "session_cleanup", "system")
        elif session_id:
            clear_session_validation_cache(session_id=session_id)
            
        # Use context manager for database operations
        with get_db_cursor() as cur:
            if session_id:
                cur.execute("DELETE FROM user_sessions WHERE session_id = %s", (session_id,))
            elif user_id:
                cur.execute("DELETE FROM user_sessions WHERE user_id = %s", (user_id,))
        
        if user_id and username:
            user_logger = get_user_logger(user_id, username)
            user_logger.info("Session cleanup completed")
    except Exception as e:
        if user_id and username:
            user_logger = get_user_logger(user_id, username)
            user_logger.error(f"Error cleaning up session: {str(e)}")
            log_critical_event("SESSION_CLEANUP_ERROR", username, str(e), user_id)
        else:
            app_logger.error(f"Error cleaning up session: {str(e)}")
            log_critical_event("SESSION_CLEANUP_ERROR", "unknown", str(e))

def cleanup_old_sessions():
    """Clean up sessions older than 30 minutes"""
    try:
        with get_db_cursor() as cur:
            cur.execute("DELETE FROM user_sessions WHERE last_activity < DATE_SUB(NOW(), INTERVAL 30 MINUTE)")
            deleted_count = cur.rowcount
        
        if deleted_count > 0:
            app_logger.info(f"Cleaned up {deleted_count} expired sessions")
        return deleted_count
    except Exception as e:
        app_logger.error(f"Error cleaning up old sessions: {e}")
        return 0

def clear_temp_session_data():
    """Clear all temporary session data"""
    temp_keys = ['temp_username', 'temp_password']
    for key in temp_keys:
        session.pop(key, None)

def clear_all_session_data():
    """Clear all session data including application data"""
    user_id = session.get('user_id')
    username = session.get('user')
    
    if user_id and username:
        # Clear user-specific data
        clear_user_data(user_id)
        # Log data clear
        user_logger = get_user_logger(user_id, username)
        user_logger.info("All session data cleared")
        log_logout_attempt(username, user_id, "session_data_cleared", "system")
    
    session_keys = [
        'user', 'user_id', 'session_id', 'is_admin',
        'temp_username', 'temp_password',
        'current_screenshot_id', 'analysis_has_been_run'
    ]
    
    for key in session_keys:
        session.pop(key, None)
    session.clear()

def check_privacy_policy_acceptance():
    """Check if current user has accepted privacy policy"""
    user_id = session.get('user_id')
    if not user_id:
        return False
        
    try:
        with get_db_cursor() as cur:
            cur.execute("SELECT privacy_accepted FROM users WHERE id = %s", (user_id,))
            result = cur.fetchone()
            return result[0] if result else False
    except Exception as e:
        app_logger.error(f"Error checking privacy policy acceptance: {e}")
        return False

def require_privacy_acceptance(f):
    """Decorator to require privacy policy acceptance for protected routes"""
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Skip privacy check for certain routes
        if request.endpoint in ['login', 'logout', 'privacy_policy', 'decline_privacy', 'request_access']:
            return f(*args, **kwargs)
            
        if not session.get('user'):
            return redirect(url_for('login'))
            
        # Skip privacy check for admins - they set the policy!
        if session.get('is_admin'):
            return f(*args, **kwargs)
            
        if not check_privacy_policy_acceptance():
            return redirect(url_for('privacy_policy'))
            
        return f(*args, **kwargs)
    return decorated_function

# ============================================================================
# DATABASE INITIALIZATION
# ============================================================================
def init_db():
    """Initialize database tables and create default admin user"""
    with app.app_context():
        with get_db_cursor() as cur:
            # Users table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    password VARCHAR(100) NOT NULL,
                    email VARCHAR(100),
                    role ENUM('user', 'admin') DEFAULT 'user',
                    designation ENUM('engineer', 'student', 'professor', 'other') DEFAULT 'engineer',
                    custom_designation VARCHAR(100) NULL,
                    privacy_accepted BOOLEAN DEFAULT FALSE,
                    privacy_accepted_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Add privacy_accepted column to existing users table if it doesn't exist
            try:
                cur.execute("ALTER TABLE users ADD COLUMN privacy_accepted BOOLEAN DEFAULT FALSE")
            except:
                pass  # Column already exists
            
            try:
                cur.execute("ALTER TABLE users ADD COLUMN privacy_accepted_at TIMESTAMP NULL")
            except:
                pass  # Column already exists
            
            # Access requests table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS access_requests (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    full_name VARCHAR(100) NOT NULL,
                    email VARCHAR(100) NOT NULL,
                    organization VARCHAR(100),
                    purpose TEXT NOT NULL,
                    designation ENUM('engineer', 'student', 'professor', 'other') DEFAULT 'engineer',
                    custom_designation VARCHAR(100) NULL,
                    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMP NULL
                )
            """)
            
            # User sessions table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    session_id VARCHAR(255) NOT NULL UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_id (user_id),
                    INDEX idx_session_id (session_id)
                )
            """)
            
            # Create default admin user if none exists
            cur.execute("SELECT * FROM users WHERE role='admin' LIMIT 1")
            admin = cur.fetchone()
            if not admin:
                cur.execute(
                    "INSERT INTO users (username, password, email, role) VALUES (%s, %s, %s, %s)",
                    ('admin', 'admin123', '<EMAIL>', 'admin')
                )
            
            # Add designation columns to existing tables if they don't exist
            try:
                cur.execute("SHOW COLUMNS FROM users LIKE 'designation'")
                if not cur.fetchone():
                    cur.execute("ALTER TABLE users ADD COLUMN designation ENUM('engineer', 'student', 'professor', 'other') DEFAULT 'engineer' AFTER role")
                    cur.execute("ALTER TABLE users ADD COLUMN custom_designation VARCHAR(100) NULL AFTER designation")
                    
                cur.execute("SHOW COLUMNS FROM access_requests LIKE 'designation'")
                if not cur.fetchone():
                    cur.execute("ALTER TABLE access_requests ADD COLUMN designation ENUM('engineer', 'student', 'professor', 'other') DEFAULT 'engineer' AFTER purpose")
                    cur.execute("ALTER TABLE access_requests ADD COLUMN custom_designation VARCHAR(100) NULL AFTER designation")
            except Exception as e:
                app_logger.warning(f"Migration warning: {e}")

# ============================================================================
# FLASK REQUEST HANDLERS
# ============================================================================
@app.before_request
def before_request():
    """Initialize request timing, perform periodic cleanup, and enforce authentication"""
    global last_cleanup_time
    
    g.start_time = time.time()
    
    # Enforce authentication and authorization first
    allowed_routes = ['login', 'request_access', 'static', 'clear_temp_session', 'logout_all_sessions', 'check_session_status']
    admin_routes = [
        'admin_dashboard', 'admin_access_requests', 'process_request', 
        'admin_active_sessions', 'admin_force_logout', 'test_json', 'delete_user'
    ]
    
    if request.endpoint not in allowed_routes:
        if not session.get('user'):
            return redirect(url_for('login'))
        
        # Validate session in database
        if not validate_user_session():
            clear_all_session_data()
            return redirect(url_for('login'))
        
        # Check admin access
        if request.endpoint in admin_routes or 'admin' in request.path:
            if not session.get('is_admin'):
                flash('Unauthorized access', 'danger')
                return redirect(url_for('home'))
    
    # Log request info for authenticated users
    user_id = session.get('user_id')
    username = session.get('user')
    if user_id and username:
        user_logger = get_user_logger(user_id, username)
        user_logger.info(f"Request: {request.method} {request.endpoint} from {request.environ.get('REMOTE_ADDR', 'unknown')}")
    
    # Perform session cleanup periodically
    current_time = time.time()
    if current_time - last_cleanup_time > CLEANUP_INTERVAL:
        try:
            cleanup_old_sessions()
            last_cleanup_time = current_time
        except Exception as e:
            app_logger.error(f"Error during periodic cleanup: {str(e)}")

@app.after_request
def after_request(response):
    """Log request performance and add cache control headers"""
    # Cache control headers for authenticated users
    if session.get('user'):
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
    
    return response

# ============================================================================
# AUTHENTICATION ROUTES
# ============================================================================
@app.route('/login', methods=['GET', 'POST'])
def login():
    """Handle user login"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        force_login = request.form.get('force_login', False)
        
        # Log login attempt
        ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
        app_logger.info(f"Login attempt for user: {username} from IP: {ip_address}")
        
        # Use context manager for database operations
        with get_db_cursor() as cur:
            cur.execute("SELECT * FROM users WHERE username=%s", (username,))
            user = cur.fetchone()
        
        if user and user[2] == password:
            user_id = user[0]
            
            # Log successful authentication with new logging system
            user_logger = get_user_logger(user_id, username)
            user_logger.info(f"Authentication successful from IP: {ip_address}")
            log_login_attempt(username, success=True, ip_address=ip_address, details="Valid credentials")
            
            # Check for existing sessions
            with get_db_cursor() as cur:
                cur.execute("SELECT COUNT(*) FROM user_sessions WHERE user_id = %s", (user_id,))
                active_sessions_count = cur.fetchone()[0]
            
            if active_sessions_count > 0 and not force_login:
                session['temp_username'] = username
                session['temp_password'] = password
                
                user_logger.warning(f"Multiple sessions detected, requesting force login from IP: {ip_address}")
                log_critical_event("MULTIPLE_SESSIONS", username, f"Active sessions: {active_sessions_count}, IP: {ip_address}", user_id)
                
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'status': 'multi_session', 
                        'message': 'Multiple sessions detected', 
                        'username': username
                    })
                else:
                    return render_template('login.html', show_force_login=True, username=username)
            
            # Create new session
            session_id = create_user_session(user_id)
            if session_id:
                session['user'] = username
                session['user_id'] = user_id
                session['is_admin'] = (user[4] == 'admin')
                session['designation'] = user[5] if len(user) > 5 and user[5] else 'engineer'
                session['custom_designation'] = user[6] if len(user) > 6 else None
                clear_temp_session_data()
                
                if force_login:
                    user_logger.info("Forced login - previous sessions terminated")
                    log_critical_event("FORCE_LOGIN", username, f"Previous sessions terminated, IP: {ip_address}", user_id)
                    flash('Welcome back! Previous sessions terminated.', 'success')
                
                user_logger.info("Login successful")
                
                # Check if admin - they don't need to accept privacy policy
                if session.get('is_admin'):
                    return redirect(url_for('admin_dashboard'))
                else:
                    # Regular users must accept privacy policy on every login
                    return redirect(url_for('privacy_policy'))
                
            else:
                user_logger.error("Failed to create session")
                log_critical_event("SESSION_CREATE_FAILED", username, f"Failed to create session, IP: {ip_address}", user_id)
                flash('Login failed. Please try again.', 'danger')
        else:
            # Log failed login attempt
            log_login_attempt(username, success=False, ip_address=ip_address, details="Invalid credentials")
            app_logger.warning(f"Failed login attempt for user: {username} from IP: {ip_address}")
            
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'status': 'error', 'message': 'Invalid username or password'})
            else:
                flash('Invalid username or password', 'danger')
    
    if session.get('temp_username') and session.get('temp_password'):
        return render_template('login.html', show_force_login=True, username=session.get('temp_username'))
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """Handle user logout"""
    user_id = session.get('user_id')
    username = session.get('user')
    ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    
    if user_id and username:
        user_logger = get_user_logger(user_id, username)
        user_logger.info("User logout initiated")
        log_logout_attempt(username, user_id, "user_initiated", ip_address)
    
    if session.get('session_id'):
        cleanup_user_session(session_id=session.get('session_id'))
    
    clear_all_session_data()
    
    response = make_response(redirect(url_for('login')))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response

@app.route('/clear_temp_session')
def clear_temp_session():
    """Clear temporary session data and redirect to login"""
    clear_temp_session_data()
    return redirect(url_for('login'))

@app.route('/logout_all_sessions', methods=['POST'])
def logout_all_sessions():
    """Allow user to logout all their sessions remotely"""
    username = request.form.get('username')
    password = request.form.get('password')
    ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    
    if not username or not password:
        flash('Username and password are required.', 'danger')
        return redirect(url_for('login'))
    
    with get_db_cursor() as cur:
        cur.execute("SELECT * FROM users WHERE username=%s", (username,))
        user = cur.fetchone()
    
    if user and user[2] == password:
        user_id = user[0]
        user_logger = get_user_logger(user_id, username)
        user_logger.warning("Remote logout all sessions initiated")
        log_logout_attempt(username, user_id, "remote_logout_all", ip_address)
        log_critical_event("REMOTE_LOGOUT_ALL", username, f"All sessions terminated remotely, IP: {ip_address}", user_id)
        
        cleanup_user_session(user_id=user_id)
        flash('All sessions have been logged out successfully. You can now login.', 'success')
    else:
        log_login_attempt(username, success=False, ip_address=ip_address, details="Invalid credentials for remote logout")
        flash('Invalid username or password.', 'danger')
    
    return redirect(url_for('login'))

@app.route('/check_session_status')
def check_session_status():
    """Check if current session is still valid - for AJAX calls"""
    session_id = session.get('session_id')
    user_id = session.get('user_id')
    
    if not session_id or not user_id:
        return jsonify({'valid': False, 'message': 'No session found'})
    
    # Force real-time validation without cache for accuracy
    try:
        with get_db_cursor() as cur:
            # Check if session exists and is not expired (within 30 minutes of last activity)
            cur.execute("""
                SELECT us.id, u.username, u.role, us.last_activity
                FROM user_sessions us
                JOIN users u ON us.user_id = u.id
                WHERE us.session_id = %s AND us.user_id = %s
                AND us.last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
            """, (session_id, user_id))
            
            result = cur.fetchone()
            if result:
                # Session is valid and not expired, update last activity
                cur.execute("UPDATE user_sessions SET last_activity = NOW() WHERE session_id = %s", (session_id,))
                return jsonify({'valid': True, 'message': 'Session is valid'})
            else:
                # Check if session exists but is expired
                cur.execute("""
                    SELECT us.id FROM user_sessions us
                    WHERE us.session_id = %s AND us.user_id = %s
                """, (session_id, user_id))
                
                expired_session = cur.fetchone()
                if expired_session:
                    # Session exists but is expired, delete it
                    cur.execute("DELETE FROM user_sessions WHERE session_id = %s", (session_id,))
                    clear_all_session_data()
                    return jsonify({'valid': False, 'message': 'Session expired due to inactivity'})
                else:
                    # Session doesn't exist at all
                    clear_all_session_data()
                    return jsonify({'valid': False, 'message': 'Session not found'})
    except Exception as e:
        app_logger.error(f"Error checking session status: {e}")
        return jsonify({'valid': False, 'message': 'Error validating session'})

# ============================================================================
# ACCESS REQUEST ROUTES
# ============================================================================
@app.route('/request_access', methods=['GET', 'POST'])
def request_access():
    """Handle access requests from new users"""
    if request.method == 'POST':
        if request.headers.get('Content-Type') == 'application/json':
            data = request.get_json()
            full_name = data['full_name']
            email = data['email']
            organization = data['organization']
            purpose = data['purpose']
            designation = data.get('designation', 'engineer')
            custom_designation = data.get('custom_designation', '')
        else:
            full_name = request.form['full_name']
            email = request.form['email']
            organization = request.form['organization']
            purpose = request.form['purpose']
            designation = request.form.get('designation', 'engineer')
            custom_designation = request.form.get('custom_designation', '')
        
        try:
            with get_db_cursor() as cur:
                cur.execute("SELECT * FROM access_requests WHERE email = %s AND status IN ('pending', 'approved')", (email,))
                existing_request = cur.fetchone()
                
                if existing_request:
                    if existing_request[5] == 'pending':
                        message = 'You already have a pending access request. We will contact you when it is processed.'
                        status = 'info'
                    else:
                        message = 'Your access request was already approved. Please check your email for credentials.'
                        status = 'info'
                else:
                    cur.execute("""
                        INSERT INTO access_requests (full_name, email, organization, purpose, designation, custom_designation)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (full_name, email, organization, purpose, designation, custom_designation))
                    message = 'Your access request has been submitted. We will contact you soon.'
                    status = 'success'
            
            if request.headers.get('Content-Type') == 'application/json':
                return jsonify({'status': status, 'message': message})
            else:
                flash(message, status)
                return redirect(url_for('login'))
        
        except Exception as e:
            app_logger.error(f"Request access error: {e}")
            error_message = 'An error occurred. Please try again.'
            
            if request.headers.get('Content-Type') == 'application/json':
                return jsonify({'status': 'error', 'message': error_message})
            else:
                flash(error_message, 'danger')
    
    return render_template('request_access.html')

# ============================================================================
# PRIVACY POLICY ROUTES
# ============================================================================
@app.route('/privacy_policy', methods=['GET', 'POST'])
def privacy_policy():
    """Handle privacy policy display and acceptance"""
    # Check if user is logged in
    if not session.get('user'):
        return redirect(url_for('login'))
    
    user_id = session.get('user_id')
    username = session.get('user')
    
    if request.method == 'POST':
        # User accepted privacy policy
        accept_privacy = request.form.get('accept_privacy')
        
        if accept_privacy:
            # Update user's privacy acceptance in database
            with get_db_cursor() as cur:
                cur.execute("""
                    UPDATE users 
                    SET privacy_accepted = TRUE, privacy_accepted_at = CURRENT_TIMESTAMP 
                    WHERE id = %s
                """, (user_id,))
            
            # Log privacy acceptance
            user_logger = get_user_logger(user_id, username)
            user_logger.info("Privacy policy accepted")
            log_critical_event("PRIVACY_ACCEPTED", username, "User accepted privacy policy", user_id)
            
            flash('Thank you for accepting our privacy policy.', 'success')
            return redirect(url_for('admin_dashboard' if session.get('is_admin') else 'home'))
        else:
            flash('You must accept the privacy policy to continue.', 'danger')
    
    return render_template('privacy_policy.html')

@app.route('/decline_privacy')
def decline_privacy():
    """Handle privacy policy decline"""
    username = session.get('user')
    user_id = session.get('user_id')
    
    if user_id and username:
        # Log privacy decline
        user_logger = get_user_logger(user_id, username)
        user_logger.info("Privacy policy declined - logging out")
        log_critical_event("PRIVACY_DECLINED", username, "User declined privacy policy", user_id)
        
        # Cleanup session
        if session.get('session_id'):
            cleanup_user_session(session_id=session.get('session_id'))
    
    clear_all_session_data()
    # Just redirect to login without parameter since popup is shown on privacy policy page
    return redirect(url_for('login'))

# ============================================================================
# MAIN APPLICATION ROUTES
# ============================================================================
@app.route('/')
def home():
    """Main home page"""
    if session.get('is_admin'):
        return redirect(url_for('admin_dashboard'))
    
    # Check if user is logged in
    if not session.get('user'):
        return redirect(url_for('login'))
    
    return render_template('home.html')

@app.route('/project_info', methods=['GET', 'POST'])
def project_info():
    """Handle project information form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        project_data = {
            'project_name': request.form.get('project_name'),
            'project_id': request.form.get('project_id'),
            'designer': request.form.get('designer'),
            'client': request.form.get('client'),
            'description': request.form.get('description'),
            'date': request.form.get('date'),
            'revision': request.form.get('revision'),
            'RSWallSystem': request.form.get('RSWallSystem'),
            'ConnectionSystem': request.form.get('ConnectionSystem')
        }
        
        # Store in user-specific storage AND session
        for key, value in project_data.items():
            set_user_data(user_id, key, value)
            session[key] = value  # <-- store in session as well
        
        set_user_data(user_id, 'project_info_data_saved', True)
        session['project_info_data_saved'] = True  # also in session
        
        user_logger.info("Project info updated")
        success_message = get_save_message('Project info')
        flash(success_message, 'success')
        return jsonify({'message': success_message})
    
    # Get data from user-specific storage
    return render_template('project_info.html',
                           project_name=get_user_data(user_id, 'project_name', ''),
                           project_id=get_user_data(user_id, 'project_id', ''),
                           designer=get_user_data(user_id, 'designer', ''),
                           client=get_user_data(user_id, 'client', ''),
                           description=get_user_data(user_id, 'description', ''),
                           date=get_user_data(user_id, 'date', ''),
                           revision=get_user_data(user_id, 'revision', ''),
                           RSWallSystem=get_user_data(user_id, 'RSWallSystem', ''),
                           ConnectionSystem=get_user_data(user_id, 'ConnectionSystem', ''))
@app.route('/geometry', methods=['GET', 'POST'])
def geometry():
    """Handle geometry form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        geometry_data = {
            'wallHeight': request.form.get('wall_height'),
            'embedmentDepth': request.form.get('embedment_depth'),
            'wallLength': request.form.get('wall_length'),
            'wallBatter': request.form.get('wall_batter'),
            'backslopeAngle': request.form.get('backslope_angle'),
            'backslopeRise': request.form.get('backslope_rise')
        }
        
        set_user_data(user_id, 'geometryData', geometry_data)
        set_user_data(user_id, 'geometry_data_saved', True)
        
        # Clear existing screenshot since geometry affects the visualization
        clear_layout_screenshot("geometry data change")
        
        user_logger.info("Geometry data updated")
        success_message = get_save_message('Geometry data')
        flash(success_message, 'success')
        return jsonify({'message': success_message})
    
    geometry_data = get_user_data(user_id, 'geometryData', {})
    
    # Pass analysis state to template to help with JavaScript reinitialization
    template_data = dict(geometry_data)
    template_data['analysis_has_been_run'] = session.get('analysis_has_been_run', False)
    
    return render_template('geometry.html', **template_data)

@app.route('/reinforcedsoil', methods=['GET', 'POST'])
def reinforcedsoil():
    """Handle reinforced soil properties form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        set_user_data(user_id, 'soil_density', request.form.get('soil_density'))
        set_user_data(user_id, 'friction_angle', request.form.get('friction_angle'))
        set_user_data(user_id, 'cohesion', request.form.get('cohesion'))
        set_user_data(user_id, 'reinforcedsoil_data_saved', True)
        
        user_logger.info("Reinforced soil data updated")
        success_message = get_save_message('Reinforced soil data')
        flash(success_message, 'success')
        return jsonify({'message': success_message})
    
    return render_template('reinforcedsoil.html',
                           soil_density=get_user_data(user_id, 'soil_density', ''),
                           friction_angle=get_user_data(user_id, 'friction_angle', ''),
                           cohesion=get_user_data(user_id, 'cohesion', ''))

@app.route('/retainedsoil', methods=['GET', 'POST'])
def retainedsoil():
    """Handle retained soil properties form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        set_user_data(user_id, 'retainedsoil_density', request.form.get('retainedsoil_density'))
        set_user_data(user_id, 'retainedfriction_angle', request.form.get('retainedfriction_angle'))
        set_user_data(user_id, 'retainedcohesion', request.form.get('retainedcohesion'))
        set_user_data(user_id, 'retainedsoil_data_saved', True)
        
        user_logger.info("Retained soil data updated")
        success_message = get_save_message('Retained soil data')
        flash(success_message, 'success')
        return jsonify({'message': success_message})
    
    return render_template('retainedsoil.html',
                           retainedsoil_density=get_user_data(user_id, 'retainedsoil_density', ''),
                           retainedfriction_angle=get_user_data(user_id, 'retainedfriction_angle', ''),
                           retainedcohesion=get_user_data(user_id, 'retainedcohesion', ''))



@app.route('/foundationsoil', methods=['GET', 'POST'])
def foundationsoil():
    """Handle foundation soil properties form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        try:
            # Get all form values
            foundationsoildensity = request.form.get('foundationsoildensity')
            foundationsoilfriction_angle = request.form.get('foundationsoilfriction_angle')
            foundationsoilcohesion = request.form.get('foundationsoilcohesion')
            eccentricity = float(request.form.get('eccentricity'))
            eccentricityseismic = float(request.form.get('eccentricityseismic'))
            watertable = request.form.get('watertable')

            # Also update session
            session['foundationsoildensity'] = foundationsoildensity
            session['foundationsoilfriction_angle'] = foundationsoilfriction_angle
            session['foundationsoilcohesion'] = foundationsoilcohesion
            session['eccentricity'] = str(eccentricity)
            session['eccentricityseismic'] = str(eccentricityseismic)
            session['watertable'] = watertable
            session.modified = True

        # Save to database
        
            set_user_data(user_id, 'foundationsoildensity', foundationsoildensity)
            set_user_data(user_id, 'foundationsoilfriction_angle', foundationsoilfriction_angle)
            set_user_data(user_id, 'foundationsoilcohesion', foundationsoilcohesion)
            set_user_data(user_id, 'eccentricity', eccentricity)
            set_user_data(user_id, 'eccentricityseismic', eccentricityseismic)
            set_user_data(user_id, 'watertable', watertable)
            set_user_data(user_id, 'foundationsoil_data_saved', True)

        except Exception as e:
            user_logger.error(f"Failed to save foundation soil data: {str(e)}")
            return jsonify({
                'error': 'Database save failed',
                'details': str(e)
            }), 500

        user_logger.info("Foundation soil data updated")
        success_message = get_save_message('Foundation soil data')
        return jsonify({
                'message': success_message,
                'data': {
                    'eccentricity': eccentricity,
                    'eccentricityseismic': eccentricityseismic
                }
            })

    # For GET requests
    return render_template('foundationsoil.html',
        density=session.get('foundationsoildensity', get_user_data(user_id, 'foundationsoildensity', '')),
        friction_angle=session.get('foundationsoilfriction_angle', get_user_data(user_id, 'foundationsoilfriction_angle', '')),
        cohesion=session.get('foundationsoilcohesion', get_user_data(user_id, 'foundationsoilcohesion', '')),
        eccentricity=session.get('eccentricity', get_user_data(user_id, 'eccentricity', '')),
        eccentricityseismic=session.get('eccentricityseismic', get_user_data(user_id, 'eccentricityseismic', '')),
        watertable=session.get('watertable', get_user_data(user_id, 'watertable', '')))




    
@app.route('/externalloads', methods=['GET', 'POST'])
def externalloads():
    """Handle external loads form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    default_strip_load = [""] * 3
    default_impact_loads = {
        "rupture": {"upper": "", "second": ""},
        "pullout": {"upper": "", "second": ""}
    }
    
    if request.method == 'POST':
        try:
            # Extract form data
            dead_loads = [request.form.get(f'dead_load{i}', '') for i in range(1, 4)]
            live_loads = [request.form.get(f'live_load{i}', '') for i in range(1, 4)]
            vertical_strip_load = [request.form.get(f'vertical_strip_load{i}', '') for i in range(1, 4)]
            horizontal_strip_load = [request.form.get(f'horizontal_strip_load{i}', '') for i in range(1, 4)]
            strip_load_width = [request.form.get(f'strip_load_width{i}', '') for i in range(1, 4)]
            strip_load_distance = [request.form.get(f'strip_load_distance{i}', '') for i in range(1, 4)]
            earthquake_acceleration = request.form.get('earthquake_acceleration', '')
            use_direct_kh = request.form.get('use_direct_kh', '')
            seismic_force = request.form.get('seismic_force', '')
            
            impact_loads = {
                "rupture": {
                    "upper": request.form.get('rupture_impact_upper', ''),
                    "second": request.form.get('rupture_impact_second', '')
                },
                "pullout": {
                    "upper": request.form.get('pullout_impact_upper', ''),
                    "second": request.form.get('pullout_impact_second', '')
                }
            }
            
            # Convert to numeric values
            externalloads_data = {
                'dead_loads': [float(x) if x else 0 for x in dead_loads],
                'live_loads': [float(x) if x else 0 for x in live_loads],
                'vertical_strip_load': [float(x) if x else 0 for x in vertical_strip_load],
                'horizontal_strip_load': [float(x) if x else 0 for x in horizontal_strip_load],
                'strip_load_width': [float(x) if x else 0 for x in strip_load_width],
                'strip_load_distance': [float(x) if x else 0 for x in strip_load_distance],
                'earthquake_acceleration': float(earthquake_acceleration) if earthquake_acceleration else 0,
                'use_direct_kh': 'use_direct_kh' in request.form,
                'seismic_force': float(seismic_force) if seismic_force and use_direct_kh else 0,
                'impact_loads': {
                    "rupture": {
                        "upper": float(impact_loads["rupture"]["upper"]) if impact_loads["rupture"]["upper"] else 0,
                        "second": float(impact_loads["rupture"]["second"]) if impact_loads["rupture"]["second"] else 0
                    },
                    "pullout": {
                        "upper": float(impact_loads["pullout"]["upper"]) if impact_loads["pullout"]["upper"] else 0,
                        "second": float(impact_loads["pullout"]["second"]) if impact_loads["pullout"]["second"] else 0
                    }
                }
            }
            
            set_user_data(user_id, 'externalloads_data', externalloads_data)
            set_user_data(user_id, 'externalloads_data_saved', True)
            
            # Clear existing screenshot since external loads affect the visualization
            clear_layout_screenshot("external loads data change")
            
            user_logger.info("External loads data updated")
            success_message = get_save_message('External loads data')
            flash(success_message, 'success')  # Add flash message for navigation
            return jsonify({'message': success_message})
        except Exception as e:
            user_logger.error(f"Error saving external loads data: {str(e)}")
            return jsonify({'message': f"Error saving external loads data: {str(e)}"}), 400
    
    externalloads_data = get_user_data(user_id, 'externalloads_data', {})
    return render_template('externalloads.html',
                           dead_loads=externalloads_data.get('dead_loads', [""] * 3),
                           live_loads=externalloads_data.get('live_loads', [""] * 3),
                           vertical_strip_load=externalloads_data.get('vertical_strip_load', default_strip_load),
                           horizontal_strip_load=externalloads_data.get('horizontal_strip_load', default_strip_load),
                           strip_load_width=externalloads_data.get('strip_load_width', default_strip_load),
                           strip_load_distance=externalloads_data.get('strip_load_distance', default_strip_load),
                           earthquake_acceleration=externalloads_data.get('earthquake_acceleration', ''),
                           use_direct_kh=externalloads_data.get('use_direct_kh', False),
                           seismic_force=externalloads_data.get('seismic_force', ''),
                           impact_loads=externalloads_data.get('impact_loads', default_impact_loads))

@app.route('/reinforcementproperties', methods=['GET', 'POST'])
def reinforcementproperties():
    """Handle reinforcement properties form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        try:
            reinforcement_data = []
            row_count = int(request.form.get('row_count', 0))
            
            for i in range(row_count):
                reinforcement_data.append({
                    'type_id': request.form.get(f'type_id_{i}', ''),
                    'name': request.form.get(f'name_{i}', ''),
                    'tult': request.form.get(f'tult_{i}', ''),
                    'rfid': request.form.get(f'rfid_{i}', ''),
                    'rfw': request.form.get(f'rfw_{i}', ''),
                    'rfcr': request.form.get(f'rfcr_{i}', ''),
                    'fs': request.form.get(f'fs_{i}', ''),
                    'pullout_interaction_coefficient': request.form.get(f'pullout_interaction_coefficient_{i}', ''),
                    'coefficient_of_direct_sliding': request.form.get(f'coefficient_of_direct_sliding_{i}', ''),
                    'scale_factor': request.form.get(f'scale_factor_{i}', '')
                })
            
            set_user_data(user_id, 'reinforcement_data', reinforcement_data)
            set_user_data(user_id, 'reinforcementproperties_data_saved', True)
            
            # Clear existing screenshot since reinforcement properties affect the visualization
            clear_layout_screenshot("reinforcement properties change")
            
            user_logger.info("Reinforcement properties updated")
            success_message = get_save_message('Reinforcement properties')
            flash(success_message, 'success')
            
            return jsonify({'status': 'success', 'message': success_message})
        
        except Exception as e:
            user_logger.error(f"Error saving reinforcement properties data: {str(e)}")
            flash(f"Error saving reinforcement properties data: {str(e)}", 'error')
            return jsonify({
                'status': 'error',
                'message': f"Error saving reinforcement properties data: {str(e)}"
            }), 400
    
    reinforcement_data = get_user_data(user_id, 'reinforcement_data', [])
    
    return render_template('reinforcementproperties.html', 
                           reinforcement_data=reinforcement_data)

@app.route('/reinforcementlayout', methods=['GET', 'POST'])
def reinforcementlayout():
    """Handle reinforcement layout form"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    
    if request.method == 'POST':
        data = request.get_json()
        set_user_data(user_id, 'reinforcement_layout_data', data)
        set_user_data(user_id, 'reinforcementlayout_data_saved', True)
        
        # Clear any existing screenshot since the layout data has changed
        # This ensures a fresh screenshot will be captured
        clear_layout_screenshot("reinforcement layout data change")
        
        user_logger.info("Reinforcement layout data updated")
        success_message = get_save_message('Reinforcement layout data')
        flash(success_message, 'success')
        return jsonify({'message': success_message})
    
    # Get geometry data for validation purposes
    geometry_data = get_user_data(user_id, 'geometryData', {})
    return render_template('reinforcementlayout.html', geometry_data=geometry_data)

@app.route('/reinforcementlayout/data', methods=['GET'])
def reinforcementlayout_data():
    """Get reinforcement layout data"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify([])
        
    data = get_user_data(user_id, 'reinforcement_layout_data', [])
    return jsonify(data)

@app.context_processor
def inject_reinforcement_data():
    """Inject reinforcement data into all templates"""
    user_id = session.get('user_id')
    if not user_id:
        return dict(reinforcement_data=[])
        
    reinforcement_data = get_user_data(user_id, 'reinforcement_data', [])
    return dict(reinforcement_data=reinforcement_data)

@app.context_processor
def inject_save_status():
    """Inject save status flags into all templates"""
    user_id = session.get('user_id')
    if not user_id:
        return dict()
        
    # Get all the data_saved flags from user data store
    save_status = {
        'project_info_data_saved': get_user_data(user_id, 'project_info_data_saved', False),
        'geometry_data_saved': get_user_data(user_id, 'geometry_data_saved', False),
        'reinforcedsoil_data_saved': get_user_data(user_id, 'reinforcedsoil_data_saved', False),
        'retainedsoil_data_saved': get_user_data(user_id, 'retainedsoil_data_saved', False),
        'foundationsoil_data_saved': get_user_data(user_id, 'foundationsoil_data_saved', False),
        'externalloads_data_saved': get_user_data(user_id, 'externalloads_data_saved', False),
        'reinforcementproperties_data_saved': get_user_data(user_id, 'reinforcementproperties_data_saved', False),
        'reinforcementlayout_data_saved': get_user_data(user_id, 'reinforcementlayout_data_saved', False),
        # Also check if data exists for additional validation
        'soil_density': get_user_data(user_id, 'soil_density', ''),
        'externalloads_data': get_user_data(user_id, 'externalloads_data', {}),
    }
    
    return save_status

# ============================================================================
# ANALYSIS AND RESULTS ROUTES
# ============================================================================
@app.route('/run_analysis_page')
def run_analysis_page():
    """Display the run analysis page"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
    
    # Check if analysis results already exist
    has_results = bool(get_user_data(user_id, 'analysis_results'))
    
    return render_template('run_analysis.html', has_existing_results=has_results)

@app.route('/run_analysis', methods=['POST'])
def run_analysis():
    """Execute the engineering analysis"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))

    user_logger = get_user_logger(user_id)
    user_logger.info("Starting analysis run")

    # Mapping of technical field names to user-friendly form names
    field_to_form_mapping = {
        'project_name': 'Project Information',
        'geometryData': 'Geometry',
        'soil_density': 'Reinforced Soil Properties',
        'friction_angle': 'Reinforced Soil Properties',
        'cohesion': 'Reinforced Soil Properties',
        'retainedsoil_density': 'Retained Soil Properties',
        'retainedfriction_angle': 'Retained Soil Properties',
        'retainedcohesion': 'Retained Soil Properties',
        'foundationsoildensity': 'Foundation Soil Properties',
        'foundationsoilfriction_angle': 'Foundation Soil Properties',
        'foundationsoilcohesion': 'Foundation Soil Properties',
        'eccentricity': 'Foundation Soil Properties',
        'eccentricityseismic': 'Foundation Soil Properties',
        'watertable': 'Foundation Soil Properties',
        'externalloads_data': 'External Loads',
        'reinforcement_data': 'Reinforcement Properties',
        'reinforcement_layout_data': 'Reinforcement Layout'
    }

    required_data = [
        'project_name', 'geometryData', 'soil_density', 'friction_angle', 'cohesion',
        'retainedsoil_density', 'retainedfriction_angle', 'retainedcohesion',
        'foundationsoildensity', 'foundationsoilfriction_angle', 'foundationsoilcohesion',
        'eccentricity', 'eccentricityseismic', 'watertable',
        'externalloads_data', 'reinforcement_data', 'reinforcement_layout_data'
    ]

    # Check for missing data using user-isolated storage
    missing_data = [key for key in required_data if not get_user_data(user_id, key)]
    if missing_data:
        # Create user-friendly error message
        missing_forms = list(set([field_to_form_mapping.get(field, field) for field in missing_data]))
        missing_forms_str = ', '.join(missing_forms)

        user_logger.warning(f"Analysis failed: Missing required data: {missing_data}")

        if len(missing_forms) == 1:
            error_message = f"Please complete the {missing_forms_str} section before running the analysis."
        else:
            error_message = f"Please complete the following sections before running the analysis: {missing_forms_str}."

        return jsonify({
            'error': error_message,
            'missing_fields': missing_data,
            'missing_forms': missing_forms
        }), 400
    
    # Validate external loads data
    externalloads_data = get_user_data(user_id, 'externalloads_data')
    if not externalloads_data:
        user_logger.warning("Analysis failed: Missing externalloads_data")
        return jsonify({
            'error': 'Please complete the External Loads section before running the analysis.',
            'missing_fields': ['externalloads_data'],
            'missing_forms': ['External Loads']
        }), 400

    external_loads_keys = [
        'dead_loads', 'live_loads', 'vertical_strip_load', 'horizontal_strip_load',
        'strip_load_width', 'strip_load_distance', 'earthquake_acceleration',
        'seismic_force', 'impact_loads'
    ]

    for key in external_loads_keys:
        if key not in externalloads_data:
            user_logger.warning(f"Analysis failed: Missing key {key} in externalloads_data")
            return jsonify({
                'error': 'Please complete all fields in the External Loads section before running the analysis.',
                'missing_fields': [key],
                'missing_forms': ['External Loads']
            }), 400

        if key == 'impact_loads':
            impact_loads = externalloads_data[key]
            if not isinstance(impact_loads, dict):
                user_logger.warning("Analysis failed: impact_loads must be a dictionary")
                return jsonify({
                    'error': 'Please complete all fields in the External Loads section before running the analysis.',
                    'missing_fields': [key],
                    'missing_forms': ['External Loads']
                }), 400

            for load_type in ['pullout', 'rupture']:
                if load_type not in impact_loads:
                    user_logger.warning(f"Analysis failed: Missing load type {load_type} in impact_loads")
                    return jsonify({
                        'error': 'Please complete all fields in the External Loads section before running the analysis.',
                        'missing_fields': [key],
                        'missing_forms': ['External Loads']
                    }), 400

                for layer in ['upper', 'second']:
                    if layer not in impact_loads[load_type]:
                        user_logger.warning(f"Analysis failed: Missing layer {layer} in impact_loads[{load_type}]")
                        return jsonify({
                            'error': 'Please complete all fields in the External Loads section before running the analysis.',
                            'missing_fields': [key],
                            'missing_forms': ['External Loads']
                        }), 400

                    value = impact_loads[load_type][layer]
                    if not isinstance(value, (int, float)):
                        user_logger.warning(f"Analysis failed: Non-numeric value in impact_loads[{load_type}][{layer}]")
                        return jsonify({
                            'error': 'Please ensure all values in the External Loads section are valid numbers.',
                            'missing_fields': [key],
                            'missing_forms': ['External Loads']
                        }), 400
        else:
            if isinstance(externalloads_data[key], list):
                if len(externalloads_data[key]) != 3:
                    user_logger.warning(f"Analysis failed: Invalid length for {key}")
                    return jsonify({
                        'error': 'Please complete all fields in the External Loads section before running the analysis.',
                        'missing_fields': [key],
                        'missing_forms': ['External Loads']
                    }), 400

                if not all(isinstance(x, (int, float)) for x in externalloads_data[key]):
                    user_logger.warning(f"Analysis failed: Non-numeric values in {key}")
                    return jsonify({
                        'error': 'Please ensure all values in the External Loads section are valid numbers.',
                        'missing_fields': [key],
                        'missing_forms': ['External Loads']
                    }), 400
            else:
                if not isinstance(externalloads_data[key], (int, float)):
                    user_logger.warning(f"Analysis failed: Non-numeric value for {key}")
                    return jsonify({
                        'error': 'Please ensure all values in the External Loads section are valid numbers.',
                        'missing_fields': [key],
                        'missing_forms': ['External Loads']
                    }), 400
    
    # Prepare user data for analysis
    user_analysis_data = {}
    for key in required_data:
        user_analysis_data[key] = get_user_data(user_id, key)
    
    # Run the analysis
    try:
        results = calculate_pressure(user_analysis_data)

        # Handle different result types
        if results is not None:
            # Check if it's a dictionary (error case)
            if isinstance(results, dict):
                analysis_results = results
            # Check if it's a Flask Response object (success case)
            elif isinstance(results, Response):
                analysis_results = results.get_json()
            else:
                analysis_results = results

            # Check if the result contains a Mononobe-Okabe error
            if isinstance(analysis_results, dict) and analysis_results.get('error_code') == 'MONONOBE_OKABE_FAILURE':
                user_logger.warning("Analysis failed: Mononobe-Okabe Method failure")
                return jsonify({
                    'error': analysis_results.get('error', 'Mononobe-Okabe Method in seismic case failed. Use GLE method and input total seismic force.'),
                    'error_code': 'MONONOBE_OKABE_FAILURE',
                    'has_results': False
                }), 400

            # Check if the result contains any other error
            if isinstance(analysis_results, dict) and 'error' in analysis_results:
                user_logger.warning(f"Analysis failed: {analysis_results.get('error', 'Unknown error')}")
                return jsonify({
                    'error': analysis_results.get('error', 'An error occurred during the analysis calculation.'),
                    'error_code': analysis_results.get('error_code', 'CALCULATION_ERROR'),
                    'has_results': False
                }), 400

            # Success case
            set_user_data(user_id, 'analysis_results', analysis_results)
            session['analysis_has_been_run'] = True

            user_logger.info("Analysis completed successfully")
            return jsonify({'message': 'Analysis completed successfully', 'has_results': True})
        else:
            user_logger.error("Analysis failed: Error occurred during calculation")
            return jsonify({
                'error': 'An error occurred during the analysis calculation. Please check your input values and try again.',
                'has_results': False
            }), 400
    except Exception as e:
        error_str = str(e)
        user_logger.error(f"Analysis failed with exception: {error_str}")

        # Check for complex number error patterns
        if "complex" in error_str.lower() or "not supported between instances" in error_str.lower():
            return jsonify({
                'error': 'Mononobe-Okabe Method in seismic case failed. Use GLE method and input total seismic force.',
                'error_code': 'MONONOBE_OKABE_FAILURE',
                'has_results': False,
                'technical_error': error_str
            }), 400

        return jsonify({
            'error': 'An unexpected error occurred during the analysis. Please check your input data and try again.',
            'has_results': False,
            'technical_error': error_str
        }), 400

@app.route('/external_stability_results')
def external_stability_results():
    """Display external stability results"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    results = get_user_data(user_id, 'analysis_results')
    if not results:
        return redirect(url_for('home'))
    return render_template('external_stability_results.html', results=results)

@app.route('/internal_stability_results')
def internal_stability_results():
    """Display internal stability results"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    results = get_user_data(user_id, 'analysis_results')
    if not results:
        return redirect(url_for('home'))
    return render_template('internal_stability_results.html', results=results)

# ============================================================================
# SCREENSHOT AND REPORT ROUTES
# ============================================================================
@app.route('/store-screenshot', methods=['POST'])
def store_screenshot():
    """Store screenshot for report generation"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'status': 'error', 'message': 'User not authenticated'}), 401
        
    user_logger = get_user_logger(user_id)
    
    try:
        data = request.get_json()
        if not data or 'screenshot' not in data:
            user_logger.warning("Screenshot store failed: Invalid data")
            return jsonify({'status': 'error', 'message': 'Invalid data'}), 400
        
        full_data_url = data['screenshot']
        if ',' not in full_data_url:
            user_logger.warning("Screenshot store failed: Invalid data URL format")
            return jsonify({'status': 'error', 'message': 'Invalid data URL format'}), 400
        
        header, base64_data = full_data_url.split(",", 1)
        
        try:
            base64.b64decode(base64_data, validate=True)
        except Exception as e:
            user_logger.warning(f"Screenshot store failed: Invalid base64: {str(e)}")
            return jsonify({'status': 'error', 'message': f'Invalid base64: {str(e)}'}), 400
        
        padded_data = add_padding(base64_data)
        processed_data_url = f"{header},{padded_data}"
        
        screenshot_id = str(uuid.uuid4())
        user_screenshot_key = get_user_screenshot_key(user_id, screenshot_id)
        layout_screenshots[user_screenshot_key] = processed_data_url
        session['current_screenshot_id'] = screenshot_id
        
        user_logger.info(f"Screenshot stored successfully: {screenshot_id[:8]}...")
        return jsonify({'status': 'success', 'screenshot_id': screenshot_id})
    except Exception as e:
        user_logger.error(f"Screenshot store failed: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/capture-layout-screenshot', methods=['POST'])
def capture_layout_screenshot():
    """Trigger automatic screenshot capture"""
    try:
        return jsonify({'status': 'success', 'message': 'Screenshot capture triggered'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/generate_report')
def generate_report():
    """Generate PDF report"""
    user_id = session.get('user_id')
    if not user_id:
        return redirect(url_for('login'))
        
    user_logger = get_user_logger(user_id)
    user_logger.info("Generating report")
    
    # Gather all data for report from user-isolated storage
    project_info = {
        'project_name': get_user_data(user_id, 'project_name', ''),
        'project_id': get_user_data(user_id, 'project_id', ''),
        'designer': get_user_data(user_id, 'designer', ''),
        'client': get_user_data(user_id, 'client', ''),
        'date': get_user_data(user_id, 'date', ''),
        'description': get_user_data(user_id, 'description', ''),
        'revision': get_user_data(user_id, 'revision', ''),
        'RSWallSystem': get_user_data(user_id, 'RSWallSystem', ''),
        'ConnectionSystem': get_user_data(user_id, 'ConnectionSystem', ''),
    }
    
    geometry_data = get_user_data(user_id, 'geometryData', {})
    geometry = {
        'wall_height': geometry_data.get('wallHeight', ''),
        'wall_length': geometry_data.get('wallLength', ''),
        'wall_batter': geometry_data.get('wallBatter', ''),
        'backslope_angle': geometry_data.get('backslopeAngle', ''),
        'backslope_rise': geometry_data.get('backslopeRise', ''),
        'embedment_depth': geometry_data.get('embedmentDepth', ''),
    }
    
    soil_properties = {
        'reinforced_soil': {
            'density': get_user_data(user_id, 'soil_density', ''),
            'friction_angle': get_user_data(user_id, 'friction_angle', ''),
            'cohesion': get_user_data(user_id, 'cohesion', ''),
        },
        'retained_soil': {
            'density': get_user_data(user_id, 'retainedsoil_density', ''),
            'friction_angle': get_user_data(user_id, 'retainedfriction_angle', ''),
            'cohesion': get_user_data(user_id, 'retainedcohesion', ''),
        },
        'foundation_soil': {
            'density': get_user_data(user_id, 'foundationsoildensity', ''),
            'friction_angle': get_user_data(user_id, 'foundationsoilfriction_angle', ''),
            'cohesion': get_user_data(user_id, 'foundationsoilcohesion', ''),
            'eccentricity': get_user_data(user_id, 'eccentricity', ''),
            'eccentricity_seismic': get_user_data(user_id, 'eccentricityseismic', ''),
            'watertable': get_user_data(user_id, 'watertable', ''),
        },
    }
    
    external_loads = get_user_data(user_id, 'externalloads_data', {})
    reinforcement_data = get_user_data(user_id, 'reinforcement_data', [])
    reinforcement_layout = get_user_data(user_id, 'reinforcement_layout_data', {})
    analysis_results = get_user_data(user_id, 'analysis_results', {})
    
    # Process screenshot - Check if we have a current screenshot
    screenshot_id = session.get('current_screenshot_id')
    processed_data_url = None
    
    if screenshot_id:
        user_screenshot_key = get_user_screenshot_key(user_id, screenshot_id)
        full_data_url = layout_screenshots.get(user_screenshot_key)
        if full_data_url:
            header, base64_data = full_data_url.split(",", 1)
            padded_data = add_padding(base64_data)
            processed_data_url = f"{header},{padded_data}"
    
    # If no screenshot available, generate diagram server-side if data is available
    if not processed_data_url:
        if has_required_data_for_visualization():
            # Generate SVG diagram server-side using user data
            user_data = {}
            for key in ['geometryData', 'externalloads_data', 'reinforcement_layout_data']:
                user_data[key] = get_user_data(user_id, key, {})
            wall_diagram_svg = generate_wall_diagram_svg(user_data)
            wall_diagram_b64 = base64.b64encode(wall_diagram_svg.encode('utf-8')).decode('utf-8')
            processed_data_url = f"data:image/svg+xml;base64,{wall_diagram_b64}"
            user_logger.info("Generated server-side diagram for report")
        else:
            placeholder_svg = '''<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f8f9fa"/>
                <text x="50%" y="45%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="24" fill="#6c757d">
                    No Visualization Available
                </text>
                <text x="50%" y="55%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#6c757d">
                    Please complete all input sections first
                </text>
            </svg>'''
            placeholder_b64 = base64.b64encode(placeholder_svg.encode('utf-8')).decode('utf-8')
            processed_data_url = f"data:image/svg+xml;base64,{placeholder_b64}"
            user_logger.info("Using placeholder diagram for report")
    
    try:
        # Generate HTML and convert to PDF
        html = render_template('report.html',
                               project_info=project_info,
                               geometry=geometry,
                               soil_properties=soil_properties,
                               external_loads=external_loads,
                               reinforcement_data=reinforcement_data,
                               reinforcement_layout=reinforcement_layout,
                               external_stability=analysis_results,
                               internal_stability={
                                   'Reinforcement Results': analysis_results.get('Reinforcement Results', []),
                                   'EQ Reinforcement Results': analysis_results.get('EQ Reinforcement Results', []),
                               },
                               layout_screenshot=processed_data_url,
                               generation_date=datetime.now().strftime("%B %d, %Y at %I:%M %p"))
        
        pdf = HTML(string=html).write_pdf()
        response = make_response(pdf)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=GRS_Report_{project_info["project_name"]}.pdf'
        
        user_logger.info("Report generated successfully")
        return response
    except Exception as e:
        user_logger.error(f"Report generation failed: {str(e)}")
        flash(f"Error generating report: {str(e)}", 'error')
        return redirect(url_for('home'))

@app.route('/check-visualization-readiness', methods=['GET'])
def check_visualization_readiness():
    """Check if we have all data needed for visualization and if screenshot exists"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'has_required_data': False, 'has_screenshot': False, 'needs_visit_layout': False})
        
    has_data = has_required_data_for_visualization()
    screenshot_id = session.get('current_screenshot_id')
    user_screenshot_key = get_user_screenshot_key(user_id, screenshot_id) if screenshot_id else None
    has_screenshot = bool(screenshot_id and user_screenshot_key in layout_screenshots)
    
    return jsonify({
        'has_required_data': has_data,
        'has_screenshot': has_screenshot,
        'needs_visit_layout': has_data and not has_screenshot
    })

# ============================================================================
# ADMIN ROUTES
# ============================================================================
@app.route('/admin/dashboard')
def admin_dashboard():
    """Admin dashboard"""
    if not session.get('user') or not session.get('is_admin'):
        flash('Unauthorized access', 'danger')
        return redirect(url_for('home'))
    
    with get_db_cursor() as cur:
        cur.execute("SELECT COUNT(*) FROM access_requests WHERE status = 'pending'")
        pending_count = cur.fetchone()[0]
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return render_template('admin_dashboard.html', pending_count=pending_count, current_time=current_time)

@app.route('/admin/access_requests')
def admin_access_requests():
    """View and manage access requests"""
    if not session.get('user') or not session.get('is_admin'):
        flash('Unauthorized access', 'danger')
        return redirect(url_for('home'))
    
    with get_db_cursor() as cur:
        cur.execute("""
            SELECT ar.*, u.username, u.password
            FROM access_requests ar
            LEFT JOIN users u ON ar.email = u.email
            ORDER BY ar.created_at DESC
        """)
        requests = cur.fetchall()
    
    return render_template('admin_access_requests.html', requests=requests)

@app.route('/admin/process_request/<int:request_id>', methods=['POST'])
def process_request(request_id):
    """Process access request (approve/reject)"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403
    
    action = request.form.get('action')
    if action not in ['approve', 'reject']:
        return jsonify({'status': 'error', 'message': 'Invalid action'}), 400
    
    try:
        with get_db_cursor() as cur:
            cur.execute("SELECT * FROM access_requests WHERE id = %s", (request_id,))
            access_req = cur.fetchone()
            
            if not access_req:
                return jsonify({'status': 'error', 'message': 'Request not found'}), 404
            
            if action == 'approve':
                username = access_req[1].split()[0].lower() + str(random.randint(100, 999))
                password = ''.join(random.choices(string.ascii_letters + string.digits, k=10))
                
                # Get designation info from access request - handle both old and new records
                try:
                    # For new records with designation columns (index 5 and 6)
                    designation = access_req[5] if len(access_req) > 5 and access_req[5] else 'engineer'
                    custom_designation = access_req[6] if len(access_req) > 6 and access_req[6] else None
                except IndexError:
                    # For old records without designation columns
                    designation = 'engineer'
                    custom_designation = None
                
                # Ensure designation is valid and not None
                valid_designations = ['engineer', 'student', 'professor', 'other']
                if not designation or designation not in valid_designations:
                    designation = 'engineer'
                
                cur.execute("INSERT INTO users (username, password, email, designation, custom_designation) VALUES (%s, %s, %s, %s, %s)",
                           (username, password, access_req[2], designation, custom_designation))
                cur.execute("UPDATE access_requests SET status = 'approved', processed_at = NOW() WHERE id = %s",
                           (request_id,))
                
                flash(f"Access granted for {access_req[1]}. Username: {username}, Password: {password}", 'success')
            else:
                cur.execute("UPDATE access_requests SET status = 'rejected', processed_at = NOW() WHERE id = %s",
                           (request_id,))
        
        return jsonify({'status': 'success'})
    
    except Exception as e:
        app_logger.error(f"Error processing access request: {str(e)}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/admin/active_sessions')
def admin_active_sessions():
    """View active user sessions"""
    if not session.get('user') or not session.get('is_admin'):
        flash('Unauthorized access', 'danger')
        return redirect(url_for('home'))
    
    with get_db_cursor() as cur:
        cur.execute("""
            SELECT us.id, u.username, us.created_at, us.last_activity, us.ip_address, us.user_agent
            FROM user_sessions us
            JOIN users u ON us.user_id = u.id
            ORDER BY us.last_activity DESC
        """)
        active_sessions = cur.fetchall()
    
    return render_template('admin_active_sessions.html', active_sessions=active_sessions)

@app.route('/admin/force_logout/<int:session_db_id>', methods=['POST'])
def admin_force_logout(session_db_id):
    """Force logout a specific session"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403
    
    admin_username = session.get('user')
    admin_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    
    try:
        with get_db_cursor() as cur:
            # Get user info for the session being terminated
            cur.execute("""
                SELECT us.id, u.username, u.id, us.session_id 
                FROM user_sessions us 
                JOIN users u ON us.user_id = u.id 
                WHERE us.id = %s
            """, (session_db_id,))
            session_info = cur.fetchone()
            
            if not session_info:
                return jsonify({'status': 'error', 'message': 'Session not found'}), 404
            
            target_username = session_info[1]
            target_user_id = session_info[2]
            
            # Log the forced logout
            target_user_logger = get_user_logger(target_user_id, target_username)
            target_user_logger.warning(f"Session force-terminated by admin: {admin_username}")
            log_logout_attempt(target_username, target_user_id, "admin_force_logout", admin_ip)
            log_critical_event("ADMIN_FORCE_LOGOUT", target_username, f"Terminated by admin: {admin_username}, IP: {admin_ip}", target_user_id)
            
            cur.execute("DELETE FROM user_sessions WHERE id = %s", (session_db_id,))
            rows_affected = cur.rowcount
            
            if rows_affected > 0:
                return jsonify({'status': 'success', 'message': 'User session terminated successfully'})
            else:
                return jsonify({'status': 'error', 'message': 'Failed to terminate session'}), 500
    
    except Exception as e:
        app_logger.error(f"Error in admin_force_logout: {str(e)}")
        log_critical_event("ADMIN_FORCE_LOGOUT_ERROR", admin_username, f"Error: {str(e)}, IP: {admin_ip}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

# ============================================================================
# ACCESS REQUEST PROCESSING ROUTES
# ============================================================================

@app.route('/admin/test_json', methods=['POST'])
def test_json():
    """Test JSON response functionality"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403
    
    return jsonify({'status': 'success', 'message': 'JSON response test successful'})

@app.route('/admin/delete_user', methods=['POST'])
def delete_user():
    """Delete a user account and all associated data"""
    if not session.get('user') or not session.get('is_admin'):
        return jsonify({'status': 'error', 'message': 'Unauthorized'}), 403
    
    email = request.form.get('email')
    if not email:
        return jsonify({'status': 'error', 'message': 'Email is required'}), 400
    
    try:
        with get_db_cursor() as cur:
            # First, get the user ID
            cur.execute("SELECT id, username FROM users WHERE email = %s", (email,))
            user = cur.fetchone()
            
            if not user:
                return jsonify({'status': 'error', 'message': 'User not found'}), 404
            
            user_id = user[0]
            username = user[1]
            
            # Prevent admin from deleting themselves
            if username == session.get('user'):
                return jsonify({'status': 'error', 'message': 'Cannot delete your own account'}), 400
            
            # Delete user sessions first (foreign key constraint)
            cur.execute("DELETE FROM user_sessions WHERE user_id = %s", (user_id,))
            
            # Delete the user
            cur.execute("DELETE FROM users WHERE id = %s", (user_id,))
        
        return jsonify({'status': 'success', 'message': f'User "{username}" deleted successfully'})
    
    except Exception as e:
        app_logger.error(f"Error deleting user: {e}")
        return jsonify({'status': 'error', 'message': str(e)}), 500

# ============================================================================
# APPLICATION INITIALIZATION AND STARTUP
# ============================================================================
if __name__ == '__main__':
    # Initialize database on startup
    init_db()
    
    # Run the application
    app.run(debug=True, host='0.0.0.0', port=5000)
