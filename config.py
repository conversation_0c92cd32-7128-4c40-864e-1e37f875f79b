import os
import logging
from datetime import timedelta

class Config:
    """Base configuration class"""
    # Flask Configuration
    SECRET_KEY = 'YourSecretKeyHere'  # IMPORTANT: Replace with a real secret key in production
    
    # Database Configuration
    MYSQL_HOST = 'localhost'
    MYSQL_USER = 'root'
    MYSQL_PASSWORD = 'sriroot'
    MYSQL_DB = 'grs_software'
    MYSQL_POOL_SIZE = 10
    MYSQL_POOL_TIMEOUT = 20
    MYSQL_POOL_RECYCLE = 3600
    MYSQL_AUTOCOMMIT = True
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # Logging Configuration
    LOG_LEVEL = logging.INFO
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_DIR = 'logs'
    
    # Application Configuration
    CACHE_TIMEOUT = 30
    CLEANUP_INTERVAL = 60
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Ensure logs directory exists
        os.makedirs(Config.LOG_DIR, exist_ok=True)
        
        # Configure logging
        Config.configure_logging()
    
    @staticmethod
    def configure_logging():
        """Configure application logging"""
        # Suppress only fontTools debug logs
        logging.getLogger('fontTools').setLevel(logging.WARNING)
        logging.getLogger('fontTools.subset').setLevel(logging.WARNING)
        logging.getLogger('fontTools.subset.timer').setLevel(logging.WARNING)
        logging.getLogger('fontTools.ttLib').setLevel(logging.WARNING)
        
        # Configure application logger
        logging.basicConfig(
            level=Config.LOG_LEVEL,
            format=Config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(os.path.join(Config.LOG_DIR, 'grs_app.log')),
                logging.StreamHandler()
            ]
        )

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # Development specific settings
    HOST = '0.0.0.0'
    PORT = 5000
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        print("Running in DEVELOPMENT mode")

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False

    # Production specific settings
    SESSION_COOKIE_SECURE = True
    LOG_LEVEL = logging.CRITICAL  # Only critical stuff in production

    @staticmethod
    def init_app(app):
        Config.init_app(app)
        print("Running in PRODUCTION mode")

        # Production-specific logging configuration - only critical logs
        logging.basicConfig(
            level=logging.CRITICAL,
            format=Config.LOG_FORMAT,
            handlers=[
                logging.FileHandler(os.path.join(Config.LOG_DIR, 'grs_app.log')),
                # Remove console handler in production for performance
            ]
        )

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """Get configuration class based on environment"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    return config.get(config_name, config['default'])
