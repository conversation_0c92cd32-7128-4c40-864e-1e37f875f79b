#!/usr/bin/env python3
"""
Development server runner
Simple script to run the application in development mode
"""

import os
import sys

if __name__ == '__main__':
    # Set environment for development BEFORE importing app
    os.environ['FLASK_ENV'] = 'development'

    # Clear any cached modules to ensure fresh import
    if 'app' in sys.modules:
        del sys.modules['app']
    if 'config' in sys.modules:
        del sys.modules['config']

    # Import app after setting environment
    from app import app

    print("Starting GRS Wall Designer in DEVELOPMENT mode...")
    print(f"Configuration: {app.config.get('DEBUG', 'Unknown')}")
    print("Server will be available at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")

    # Run the application in development mode
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000,
        use_reloader=True
    )
