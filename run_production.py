#!/usr/bin/env python3
"""
Production server runner with fallback mechanisms
Attempts to run production server, falls back to development, then maintenance page
"""

import os
import sys
import time
import signal
import subprocess
import threading
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse
import socket
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/production_runner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MaintenanceHandler(SimpleHTTPRequestHandler):
    """Custom handler to serve maintenance page"""
    
    def do_GET(self):
        """Serve maintenance page for all GET requests"""
        try:
            with open('templates/maintenance.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.send_header('Content-Length', str(len(content.encode('utf-8'))))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except Exception as e:
            logger.error(f"Error serving maintenance page: {e}")
            self.send_error(500, "Internal Server Error")
    
    def do_POST(self):
        """Handle POST requests during maintenance"""
        self.do_GET()
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"Maintenance server: {format % args}")

def check_port_available(port):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', port))
            return True
    except OSError:
        return False

def wait_for_server(host, port, timeout=30):
    """Wait for server to become available"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex((host, port))
                if result == 0:
                    return True
        except Exception:
            pass
        time.sleep(1)
    return False

def run_production_server():
    """Run production server using gunicorn (Linux only)"""
    logger.info("Attempting to start production server with gunicorn...")

    # Set environment for production
    os.environ['FLASK_ENV'] = 'production'

    # Check if port 5000 is available
    if not check_port_available(5000):
        logger.warning("Port 5000 is already in use")
        return False

    try:
        # Run gunicorn command
        cmd = [
            'gunicorn',
            'app:app',
            '--bind', '0.0.0.0:5000',
            '--workers', '1',
            '--timeout', '120',
            '--access-logfile', 'logs/gunicorn_access.log',
            '--error-logfile', 'logs/gunicorn_error.log',
            '--log-level', 'critical'  # Only critical logs in production
        ]

        logger.info(f"Starting gunicorn with command: {' '.join(cmd)}")

        # Start gunicorn process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # Wait a bit for server to start
        time.sleep(3)

        # Check if process is still running
        if process.poll() is None:
            # Check if server is responding
            if wait_for_server('localhost', 5000, timeout=10):
                logger.info("Production server started successfully")

                # Keep the process running
                try:
                    process.wait()
                except KeyboardInterrupt:
                    logger.info("Received interrupt signal, shutting down production server...")
                    process.terminate()
                    process.wait()

                return True
            else:
                logger.error("Production server started but not responding")
                process.terminate()
                return False
        else:
            # Process died, get error output
            stdout, stderr = process.communicate()
            logger.error(f"Production server failed to start")
            logger.error(f"STDOUT: {stdout}")
            logger.error(f"STDERR: {stderr}")
            return False

    except FileNotFoundError:
        logger.error("gunicorn not found. Please install it: pip install gunicorn")
        return False
    except Exception as e:
        logger.error(f"Error starting production server: {e}")
        return False

def run_development_server():
    """Run development server as fallback"""
    logger.info("Attempting to start development server...")
    
    # Set environment for development
    os.environ['FLASK_ENV'] = 'development'
    
    try:
        # Import and run Flask app directly
        from app import app
        logger.info("Development server started successfully")
        app.run(debug=False, host='0.0.0.0', port=5000)
        return True
    except Exception as e:
        logger.error(f"Error starting development server: {e}")
        return False

def run_maintenance_server():
    """Run maintenance page server as final fallback"""
    logger.info("Starting maintenance page server...")
    
    try:
        # Create maintenance server
        server = HTTPServer(('0.0.0.0', 5000), MaintenanceHandler)
        logger.info("Maintenance server started on http://0.0.0.0:5000")
        
        # Handle shutdown gracefully
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal, stopping maintenance server...")
            server.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start server
        server.serve_forever()
        
    except Exception as e:
        logger.error(f"Error starting maintenance server: {e}")
        return False

def main():
    """Main function to run servers with fallback"""
    logger.info("=== GRS Wall Designer Production Runner ===")
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    # Try production server first
    logger.info("Step 1: Trying production server...")
    if run_production_server():
        return
    
    logger.warning("Production server failed, falling back to development server...")
    
    # Try development server
    logger.info("Step 2: Trying development server...")
    try:
        if run_development_server():
            return
    except KeyboardInterrupt:
        logger.info("Development server interrupted")
        return
    
    logger.error("Development server failed, falling back to maintenance page...")
    
    # Final fallback: maintenance page
    logger.info("Step 3: Starting maintenance page...")
    run_maintenance_server()

if __name__ == '__main__':
    main()
