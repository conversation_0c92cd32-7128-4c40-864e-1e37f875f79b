/* mobile-optimizations.css */ /* Mobile-First Optimizations for GRS Wall Designer */ /* Table Responsiveness */ .table-responsive{overflow-x:auto;-webkit-overflow-scrolling:touch}table{width:100%;border-collapse:collapse}@media (max-width:768px){/* Stack table cells vertically on mobile */ .mobile-stack table,.mobile-stack thead,.mobile-stack tbody,.mobile-stack th,.mobile-stack td,.mobile-stack tr{display:block}.mobile-stack thead tr{position:absolute;top:-9999px;left:-9999px}.mobile-stack tr{border:1px solid #ccc;margin-bottom:10px;padding:10px;border-radius:8px;background:white}.mobile-stack td{border:none;position:relative;padding:8px 8px 8px 50%;text-align:left}.mobile-stack td:before{content:attr(data-label) ":";position:absolute;left:6px;width:45%;padding-right:10px;white-space:nowrap;font-weight:bold;color:#333}}/* Form Optimizations */ @media (max-width:768px){.form-row{flex-direction:column}.form-row .col{width:100%;margin-bottom:15px}.input-group{flex-direction:column}.input-group-text{border-radius:0.375rem 0.375rem 0 0;border-bottom:none}.input-group .form-control{border-radius:0 0 0.375rem 0.375rem}}/* Card Optimizations */ @media (max-width:768px){.card{margin-bottom:15px;border-radius:12px}.card-body{padding:1rem}.card-header{padding:0.75rem 1rem;font-size:1.1rem}}/* Button Groups */ @media (max-width:768px){.btn-group{flex-direction:column;width:100%}.btn-group .btn{margin-bottom:8px;border-radius:0.375rem !important}.btn-group .btn:last-child{margin-bottom:0}}/* Navigation Improvements */ @media (max-width:768px){.nav-tabs{flex-direction:column;border-bottom:none}.nav-tabs .nav-link{border:1px solid #dee2e6;border-radius:0.375rem;margin-bottom:5px;text-align:center}.nav-tabs .nav-link.active{background-color:var(--primary-color);color:white;border-color:var(--primary-color)}}/* Modal Optimizations */ @media (max-width:768px){.modal-dialog{margin:10px;max-width:calc(100% - 20px)}.modal-content{border-radius:12px}.modal-header{padding:1rem;border-bottom:1px solid #dee2e6}.modal-body{padding:1rem;max-height:60vh;overflow-y:auto}.modal-footer{padding:1rem;flex-direction:column}.modal-footer .btn{width:100%;margin-bottom:8px}.modal-footer .btn:last-child{margin-bottom:0}}/* Alert Optimizations */ @media (max-width:768px){.alert{margin:10px;border-radius:8px;font-size:14px}}/* Utility Classes for Mobile */ @media (max-width:768px){.mobile-hidden{display:none !important}.mobile-full-width{width:100% !important}.mobile-text-center{text-align:center !important}.mobile-no-padding{padding:0 !important}.mobile-small-padding{padding:0.5rem !important}}/* Touch-friendly interactions */ @media (hover:none) and (pointer:coarse){.btn:hover{transform:none}.btn:active{transform:scale(0.98)}.card:hover{transform:none}.menu-item:hover{background-color:transparent}.menu-item:active{background-color:rgba(37,99,235,0.1)}}/* Prevent zoom on input focus (iOS) */ @media screen and (max-width:768px){input[type="text"],input[type="number"],input[type="email"],input[type="password"],textarea,select{font-size:16px !important}}/* Improve scrolling performance */ .scroll-container{-webkit-overflow-scrolling:touch;overflow-scrolling:touch}/* Fix for iOS Safari bottom bar */ @supports (-webkit-touch-callout:none){.ios-safe-area{padding-bottom:env(safe-area-inset-bottom)}}/* home.css */ /* Modern Home Page Styles */ .home-container{max-width:1200px;margin:0 auto;padding:0 1rem}.hero{background:linear-gradient(135deg,var(--primary-color) 0%,var(--primary-dark) 100%);color:white;text-align:center;padding:4rem 2rem;margin-bottom:3rem;border-radius:var(--radius-lg);box-shadow:var(--shadow-lg);position:relative;overflow:hidden}.hero::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent 45%,rgba(255,255,255,0.05) 49%,rgba(255,255,255,0.05) 51%,transparent 51%),linear-gradient(0deg,transparent 45%,rgba(255,255,255,0.05) 49%,rgba(255,255,255,0.05) 51%,transparent 51%);background-size:30px 30px;opacity:0.3}.hero h1{font-size:3rem;font-weight:800;margin-bottom:1.5rem;text-shadow:0 4px 8px rgba(0,0,0,0.3);position:relative;z-index:2;background:linear-gradient(45deg,#ffffff,#e2e8f0);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.hero .tagline{font-size:1.25rem;font-weight:400;margin-bottom:2rem;opacity:0.95;position:relative;z-index:2;max-width:800px;margin-left:auto;margin-right:auto;line-height:1.7}.features{display:flex;flex-wrap:wrap;gap:2rem;margin:3rem 0;justify-content:center}.feature{flex:0 1 calc(33.333% - 1.333rem);min-width:300px;max-width:400px}.feature{background:white;padding:2.5rem;border-radius:var(--radius-lg);box-shadow:var(--shadow-md);border:1px solid var(--gray-200);text-align:center;transition:all var(--transition-normal);position:relative;overflow:hidden}.feature::before{content:'';position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,var(--primary-color),var(--accent-color));transform:scaleX(0);transition:transform var(--transition-normal)}.feature:hover{transform:translateY(-8px);box-shadow:var(--shadow-lg)}.feature:hover::before{transform:scaleX(1)}.feature i{font-size:3.5rem;color:var(--primary-color);margin-bottom:1.5rem;display:block;background:linear-gradient(135deg,var(--primary-color),var(--primary-light));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.feature h2{font-size:1.5rem;font-weight:700;margin-bottom:1rem;color:var(--gray-800)}.feature p{color:var(--gray-600);line-height:1.7;font-size:0.95rem}.cta{background:linear-gradient(135deg,var(--gray-50) 0%,white 100%);text-align:center;padding:4rem 2rem;margin-top:4rem;border-radius:var(--radius-lg);border:2px solid var(--gray-200);box-shadow:var(--shadow-md);position:relative}.cta::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(135deg,var(--primary-color)/5,var(--accent-color)/5);border-radius:var(--radius-lg);z-index:1}.cta>*{position:relative;z-index:2}.cta h2{font-size:2.5rem;font-weight:800;margin-bottom:1.5rem;color:var(--gray-800);background:linear-gradient(135deg,var(--primary-color),var(--accent-color));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.cta p{font-size:1.125rem;color:var(--gray-600);margin-bottom:2.5rem;max-width:600px;margin-left:auto;margin-right:auto;line-height:1.7}.cta-button{display:inline-flex;align-items:center;gap:0.5rem;background:linear-gradient(135deg,var(--success-color),var(--accent-color));color:white;padding:1rem 2rem;text-decoration:none;border-radius:var(--radius-md);font-weight:700;font-size:1.125rem;transition:all var(--transition-normal);box-shadow:var(--shadow-md);border:none;cursor:pointer}.cta-button:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg);color:white !important;text-decoration:none;background:linear-gradient(135deg,#22c55e,#10b981) !important}.cta-button:active{transform:translateY(0)}/* Workflow Section Styling */ .workflow-section{margin-top:2rem}/* Analysis Results Styling */ .analysis-results-section{margin-top:2rem;padding:2rem;background:linear-gradient(135deg,var(--success-color)/5,var(--primary-color)/5);border-radius:var(--radius-lg);box-shadow:var(--shadow-md);border:1px solid var(--success-color)/20;text-align:center}.analysis-results-section h3{font-size:1.75rem;font-weight:700;color:var(--success-color);margin-bottom:1.5rem;display:flex;align-items:center;justify-content:center;gap:0.5rem}.analysis-results-section h3::before{content:'✓';background:var(--success-color);color:white;width:2rem;height:2rem;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:bold}.result-buttons-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem;margin-bottom:2rem}.result-btn{padding:1rem 1.5rem;font-weight:600;border-radius:var(--radius-md);transition:all var(--transition-normal);text-decoration:none}.result-btn:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg);text-decoration:none}.satisfaction-section{margin:2rem 0;padding:1.5rem;background:white;border-radius:var(--radius-md);box-shadow:var(--shadow-sm)}.satisfaction-label{display:flex;align-items:center;justify-content:center;gap:0.75rem;font-size:1.125rem;font-weight:600;color:var(--gray-700);cursor:pointer}.form-check-input{width:1.25rem;height:1.25rem;accent-color:var(--primary-color)}.generate-report-section{margin-top:1.5rem}.report-btn{padding:1rem 2rem;font-size:1.125rem;font-weight:700;border-radius:var(--radius-md);transition:all var(--transition-normal)}.report-btn:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg)}.error-message{margin-top:1.5rem;border-radius:var(--radius-md);box-shadow:var(--shadow-sm)}/* Responsive Design */ @media (max-width:1024px){.feature{flex:0 1 calc(50% - 1rem);min-width:280px}}@media (max-width:768px){.home-container{padding:0 0.5rem}.hero h1{font-size:2.5rem}.hero .tagline{font-size:1.125rem}.features{flex-direction:column;gap:1.5rem}.feature{flex:1;min-width:unset;max-width:unset}.feature{padding:2rem}.cta h2{font-size:2rem}.cta p{font-size:1rem}.result-buttons-grid{grid-template-columns:1fr;gap:1rem}.analysis-results-section{padding:1.5rem}.satisfaction-section{padding:1rem}}@media (max-width:480px){.hero{padding:3rem 1.5rem}.hero h1{font-size:2rem}.feature{padding:1.5rem}.cta{padding:3rem 1.5rem}}/* project_info.css */ /* Modern Project Info Styles */ .content-area h1{font-size:2rem;font-weight:700;color:var(--gray-800);margin-bottom:2rem;text-align:center}form{max-width:600px;margin:0 auto;background:white;padding:2.5rem;border-radius:var(--radius-lg);box-shadow:var(--shadow-lg);border:1px solid var(--gray-200)}.form-group{margin-bottom:1.5rem}label{display:block;margin-bottom:0.5rem;font-size:0.875rem;font-weight:600;color:var(--gray-700);letter-spacing:0.025em}input[type="text"],input[type="number"]{width:100%;padding:0.875rem;border:2px solid var(--gray-300);border-radius:var(--radius-md);background-color:white;font-size:1rem;color:var(--gray-800);transition:all var(--transition-fast);box-sizing:border-box}input[type="text"]:focus,input[type="number"]:focus{border-color:var(--primary-color);box-shadow:0 0 0 3px rgb(37 99 235 / 0.1);outline:none;background-color:white}input.invalid{border-color:var(--danger-color);box-shadow:0 0 0 3px rgb(239 68 68 / 0.1)}.error-message{color:var(--danger-color);font-size:0.875rem;font-weight:500;margin-top:0.5rem;display:block}button{background:linear-gradient(135deg,var(--primary-color),var(--primary-dark));color:white;padding:0.875rem 2rem;border:none;border-radius:var(--radius-md);font-size:1rem;font-weight:600;cursor:pointer;margin-top:1.5rem;margin-right:1rem;transition:all var(--transition-normal);box-shadow:var(--shadow-md)}button:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg)}button:active{transform:translateY(0)}#save-button{background:linear-gradient(135deg,var(--success-color),var(--accent-color))}#save-button:hover{background:linear-gradient(135deg,#16a34a,#059669)}/* Back button styles removed - button no longer exists */ /* Form Actions */ .form-actions{display:flex;justify-content:space-between;align-items:center;margin-top:2rem;padding-top:1.5rem;border-top:1px solid var(--gray-200)}/* Responsive Design */ @media (max-width:768px){form{margin:0 1rem;padding:2rem}.form-actions{flex-direction:column;gap:1rem}/* Back button mobile styles removed - button no longer exists */ button{width:100%;margin-right:0}}/* geometry.css */ .form-group{margin-bottom:15px}label{font-size:16px;color:#4a4a4a;font-weight:600;display:block;margin-bottom:5px}input[type="number"]{padding:8px;border:2px solid #ccc;border-radius:5px;background-color:#f9f9f9;font-size:14px;color:#333;width:100%;box-sizing:border-box}/* Mobile optimizations */ @media (max-width:768px){input[type="number"]{padding:12px;font-size:16px;min-height:44px}label{font-size:18px;margin-bottom:8px}button[type="submit"]{padding:12px 24px;font-size:16px;width:100%;min-height:48px}.form-group{margin-bottom:20px}.note{font-size:14px;padding:12px}}input[type="number"]:focus{border:2px solid #66afe9;background-color:#ffffff}button[type="submit"]{background-color:#4CAF50;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold;cursor:pointer}button[type="submit"]:hover{background-color:#45a049}.note{font-size:13px;color:#555;padding:10px;background-color:#f0f8ff;border-left:4px solid #ffa500;border-radius:5px;margin-top:8px}/* reinforcedsoil.css */ #reinforcedsoil-form .form-group{margin-bottom:15px}#reinforcedsoil-form label{font-size:16px;color:#4a4a4a;font-weight:600;display:block;margin-bottom:5px}#reinforcedsoil-form input[type="number"]{padding:8px;border:2px solid #ccc;border-radius:5px;background-color:#f9f9f9;font-size:14px;color:#333;width:100%}#reinforcedsoil-form input[type="number"]:focus{border:2px solid #66afe9;background-color:#ffffff}#reinforcedsoil-form button{background-color:#4CAF50;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold}#reinforcedsoil-form button:hover{background-color:#45a049}/* Note Styling */ #reinforcedsoil-form .note{font-size:13px;color:#555;padding:10px;background-color:#f0f8ff;border-left:4px solid #ffa500;border-radius:5px;margin-top:8px}/* retainedsoil.css */ #retainedsoil-form .form-group{margin-bottom:15px}#retainedsoil-form label{font-size:16px;color:#4a4a4a;font-weight:600;display:block;margin-bottom:5px}#retainedsoil-form input[type="number"]{padding:8px;border:2px solid #ccc;border-radius:5px;background-color:#f9f9f9;font-size:14px;color:#333;width:100%}#retainedsoil-form input[type="number"]:focus{border:2px solid #66afe9;background-color:#ffffff}#retainedsoil-form button{background-color:#4CAF50;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold}#retainedsoil-form button:hover{background-color:#45a049}/* Note Styling */ #retainedsoil-form .note{font-size:13px;color:#555;padding:10px;background-color:#f0f8ff;border-left:4px solid #ffa500;border-radius:5px;margin-top:8px}/* foundationsoil.css */ #foundationsoil-form .form-group{margin-bottom:15px}#foundationsoil-form label{font-size:16px;color:#4a4a4a;font-weight:600;display:block;margin-bottom:5px}#foundationsoil-form input[type="number"]{padding:8px;border:2px solid #ccc;border-radius:5px;background-color:#f9f9f9;font-size:14px;color:#333;width:100%;outline:none;/* Prevents blue glow in some browsers */}#foundationsoil-form input[type="number"]:focus{border:2px solid #66afe9;background-color:#ffffff}#foundationsoil-form button{background-color:#4CAF50;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold;cursor:pointer;/* Adds a pointer cursor for better UX */ transition:background-color 0.3s ease-in-out;/* Smooth hover effect */}#foundationsoil-form button:hover{background-color:#45a049}/* Note Styling */ #foundationsoil-form .note{font-size:13px;color:#555;padding:10px;background-color:#f0f8ff;border-left:4px solid #ffa500;border-radius:5px;margin-top:8px}/* externalloads.css */ #externalloadsform{background-color:white;padding:20px;border-radius:10px;box-shadow:0px 0px 10px rgba(0,0,0,0.1);width:100%;max-width:800px;/* Increase max width to fit side-by-side */ margin:auto}/* Flex container for side-by-side stacking */ #externalloadsform .form-row{display:flex;justify-content:space-between;/* Spreads items evenly */ gap:15px;/* Space between input groups */ flex-wrap:wrap;/* Ensures responsiveness on smaller screens */}#externalloadsform .form-group{flex:1;/* Equal width for each item */ min-width:250px;/* Prevents elements from shrinking too much */}#externalloadsform label{font-size:16px;color:#4a4a4a;font-weight:600;display:block;margin-bottom:5px}#externalloadsform input[type="number"]{padding:8px;border:2px solid #ccc;border-radius:5px;background-color:#f9f9f9;font-size:14px;color:#333;width:100%;outline:none}#externalloadsform input[type="number"]:focus{border:2px solid #66afe9;background-color:#ffffff}#externalloadsform button{background-color:#007BFF;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold;cursor:pointer;transition:background-color 0.3s ease-in-out;display:block;margin:20px auto;/* Centers the button */}/* Styling for notes in External Loads Form */ #externalloadsform .note-label{font-size:13px;color:#555;padding:10px;background-color:#f0f8ff;border-left:4px solid #ffa500;border-radius:5px;margin-top:8px;display:none;/* Initially hidden */}#externalloadsform button:hover{background-color:#0056b3}/* reinforcementproperties.css */ /* General Container Styling */ #reinforcementproperties-container{width:95%;/* Increased width to utilize available space */ max-width:2000px;/* Reasonable maximum width */ margin:20px auto;/* Center the container */ padding:20px;box-sizing:border-box;/* Include padding and border in the element's total width and height */}/* Form Styling */ #reinforcementproperties-form{width:130%;/* Form takes the full width of its container */ max-width:none;/* Remove max-width to allow expansion */ margin:0;/* Reset margins */ padding:0;/* Reset padding */ box-sizing:border-box}/* Table Container Styling */ .table-responsive{overflow-x:auto;/* Enable horizontal scrolling if the table exceeds the container width */ border-radius:8px;background:#f8f9fa;padding:15px;box-sizing:border-box}/* Table Styling */ #reinforcementTable{width:100%;/* Table spans full width */ border-collapse:collapse;/* Collapses the borders into a single border */ table-layout:fixed;/* Ensure that the columns widths remain the same */}#reinforcementTable th,#reinforcementTable td{text-align:center;padding:8px;border:1px solid #ddd;box-sizing:border-box;/* Ensures padding and border are included in the element's total width and height */}#reinforcementTable thead{background-color:#e3f2fd;font-weight:bold}/* Input Fields Styling */ #reinforcementTable input[type="text"],#reinforcementTable input[type="number"]{width:100%;padding:6px;border:2px solid #ccc;border-radius:5px;font-size:14px;text-align:center;box-sizing:border-box;/* Ensures padding and border are included */}#reinforcementTable input[type="number"]:focus,#reinforcementTable input[type="text"]:focus{border:2px solid #007bff;background-color:#ffffff}/* Adjust width of specific columns */ #reinforcementTable th:nth-child(1),/* Type ID */ #reinforcementTable td:nth-child(1){width:80px}#reinforcementTable th:nth-child(2),/* Name */ #reinforcementTable td:nth-child(2){width:120px}#reinforcementTable th:nth-child(3),/* Tult (kN/m) */ #reinforcementTable td:nth-child(3){width:100px}#reinforcementTable th:nth-child(4),/* RF ID */ #reinforcementTable td:nth-child(4){width:80px}#reinforcementTable th:nth-child(5),/* RF W */ #reinforcementTable td:nth-child(5){width:80px}#reinforcementTable th:nth-child(6),/* RF CR */ #reinforcementTable td:nth-child(6){width:80px}#reinforcementTable th:nth-child(7),/* FS */ #reinforcementTable td:nth-child(7){width:60px}#reinforcementTable th:nth-child(8),/* Pullout Angle */ #reinforcementTable td:nth-child(8){width:110px}#reinforcementTable th:nth-child(9),/* Sliding Angle */ #reinforcementTable td:nth-child(9){width:110px}#reinforcementTable th:nth-child(10),/* Scale Factor */ #reinforcementTable td:nth-child(10){width:90px}#reinforcementTable th:nth-child(11),/* Remove Button */ #reinforcementTable td:nth-child(11){width:80px}/* Buttons Styling */ .btn-add{background-color:#28a745;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold;cursor:pointer;transition:background-color 0.3s ease-in-out;display:block;margin:20px auto}.btn-add:hover{background-color:#218838}.btn-remove{background-color:#dc3545;color:white;padding:5px 10px;border:none;border-radius:5px;font-size:10px;font-weight:bold;cursor:pointer;transition:background-color 0.3s ease-in-out}.btn-remove:hover{background-color:#c82333}/* Media Query for Smaller Screens */ @media (max-width:768px){#reinforcementproperties-container{width:100%;/* Take full width on smaller screens */ padding:10px}#reinforcementTable th,#reinforcementTable td{font-size:12px;/* Reduce font size */ padding:6px}}/* reinforcementlayout.css */ /* Container Styling */ #reinforcementlayout-form{margin-top:20px}#reinforcementlayout-form h2{font-size:24px;color:#333;font-weight:bold;text-align:center}/* Table Styling */ .table-container{overflow-x:auto;border-radius:8px;background:#f8f9fa;padding:15px}#reinforcementLayoutTable{width:100%;border-collapse:collapse}#reinforcementLayoutTable th,#reinforcementLayoutTable td{text-align:center;padding:8px;border:1px solid #ddd}#reinforcementLayoutTable thead{background-color:#e3f2fd;font-weight:bold}/* Input Fields */ #reinforcementLayoutTable input[type="number"]{width:100%;padding:6px;border:2px solid #ccc;border-radius:5px;font-size:14px;text-align:center}#reinforcementLayoutTable input[type="number"]:focus{border:2px solid #007bff;background-color:#ffffff}/* Select Dropdown */ #reinforcementLayoutTable select{width:100%;padding:6px;border:2px solid #ccc;border-radius:5px;font-size:14px}/* Buttons */ .btn-add{background-color:#28a745;color:white;padding:10px 20px;border:none;border-radius:8px;font-size:14px;font-weight:bold;cursor:pointer;transition:background-color 0.3s ease-in-out;display:block;margin:20px auto}.btn-add:hover{background-color:#218838}.btn-remove{background-color:#dc3545;color:white;padding:5px 10px;border:none;border-radius:5px;font-size:12px;font-weight:bold;cursor:pointer;transition:background-color 0.3s ease-in-out}.btn-remove:hover{background-color:#c82333}/* results.css */ /* Consolidated Results Styles - Used by both external and internal stability results */ /* Note:Body styles removed to avoid conflicts with base.html styles */ h2,h3{color:#333}.card{margin-bottom:20px;border:1px solid #ddd;border-radius:5px;box-shadow:0 2px 4px rgba(0,0,0,0.1)}.card-header{background-color:#007bff;color:white;padding:10px;border-bottom:1px solid #ddd}.card-body{padding:20px}.table{width:100%;margin-bottom:1rem;color:#212529;border-collapse:collapse}.table th,.table td{padding:0.75rem;vertical-align:top;border-top:1px solid #dee2e6}.table thead th{vertical-align:bottom;border-bottom:2px solid #dee2e6}.table-striped tbody tr:nth-of-type(odd){background-color:rgba(0,0,0,0.05)}.text-danger{color:#dc3545}.text-success{color:#28a745}.alert{padding:15px;margin-bottom:20px;border:1px solid transparent;border-radius:4px}.alert-info{color:#0c5460;background-color:#d1ecf1;border-color:#bee5eb}.btn{padding:10px 20px;font-size:16px;border-radius:5px;text-decoration:none;color:white;background-color:#6c757d;border:none;cursor:pointer}.btn-secondary{background-color:#6c757d}.btn-secondary:hover{background-color:#5a6268}/* Results container styles */ .results-container{display:flex;flex-wrap:wrap;gap:20px;margin-top:20px}.result-item{flex:1 1 300px;/* Each item takes up at least 300px or 1/3 of the container */ border:1px solid #ddd;padding:10px;border-radius:5px;background-color:#f9f9f9}.result-item strong{display:block;margin-bottom:5px;color:#333}.result-item span,.result-item pre{display:block;word-break:break-word;/* Prevents long words from breaking the layout */ white-space:pre-wrap;/* Preserves formatting and line breaks */ color:#555}/* Optional:Style for pre elements to make them more readable */ .result-item pre{background-color:#eee;padding:5px;border-radius:3px;overflow-x:auto;/* Add horizontal scroll for long content */}/* Enhanced mobile responsive styling */ @media (max-width:992px){.results-container{gap:15px}.result-item{flex:1 1 calc(50% - 10px);/* Two columns on tablets */}}@media (max-width:768px){.results-container{gap:12px;margin-top:15px}.result-item{flex:1 1 100%;/* Full width on mobile */ padding:15px;font-size:16px}.result-item strong{font-size:18px;margin-bottom:8px}.result-item pre{padding:8px;font-size:14px}}@media (max-width:480px){.results-container{gap:10px;margin-top:10px}.result-item{padding:12px}.result-item strong{font-size:16px}}/* report.css */ /* General Styles */ body{line-height:1.6;color:#333;background-color:#f9f9f9;margin:0;padding:20px}.report-container{max-width:1200px;margin:0 auto;background:#fff;padding:20px;box-shadow:0 0 10px rgba(0,0,0,0.1)}header{text-align:center;margin-bottom:30px}header h1{font-size:28px;color:#2c3e50;margin-bottom:10px}.generation-date{font-size:14px;color:#777}/* Section Styles */ .section{margin-bottom:30px}.section h2{font-size:24px;color:#34495e;border-bottom:2px solid #34495e;padding-bottom:5px;margin-bottom:15px}.section h3{font-size:20px;color:#2c3e50;margin-top:20px;margin-bottom:10px}/* Table Styles */ .data-table{width:100%;border-collapse:collapse;margin-bottom:20px}.data-table th,.data-table td{padding:10px;border:1px solid #ddd;text-align:left}.data-table th{background-color:#34495e;color:#fff;font-weight:bold}.data-table tr:nth-child(even){background-color:#f9f9f9}.data-table tr:hover{background-color:#f1f1f1}/* Success and Danger Classes */ .text-success{color:#28a745;font-weight:bold}.text-danger{color:#dc3545;font-weight:bold}/* Footer Note */ .footer-note{margin-top:30px;padding:15px;background-color:#f1f1f1;border-left:5px solid #34495e}.footer-note p{margin:0;font-size:14px;color:#555}/* run_analysis.css */ /* Run Analysis Page Styles */ .run-analysis-container{max-width:1200px;margin:0 auto;padding:2rem}.analysis-header{text-align:center;margin-bottom:3rem}.analysis-header h1{color:#2563eb;font-size:2.5rem;font-weight:700;margin-bottom:1rem}.analysis-description{font-size:1.1rem;color:#6b7280;max-width:600px;margin:0 auto;line-height:1.6}.analysis-workflow{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:2rem;margin-bottom:3rem}.workflow-step{display:flex;align-items:flex-start;gap:1rem;padding:1.5rem;background:#f8fafc;border-radius:12px;border:1px solid #e2e8f0;transition:all 0.3s ease}.workflow-step:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,0.1)}.step-icon{flex-shrink:0;width:48px;height:48px;background:#2563eb;border-radius:50%;display:flex;align-items:center;justify-content:center;color:white;font-size:1.2rem}.step-content h3{color:#1f2937;font-size:1.2rem;font-weight:600;margin-bottom:0.5rem}.step-content p{color:#6b7280;font-size:0.95rem;line-height:1.5;margin:0}.analysis-section{background:white;border-radius:16px;box-shadow:0 4px 6px -1px rgba(0,0,0,0.1);overflow:hidden}.analysis-card{padding:3rem;text-align:center;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:white}.analysis-card h2{font-size:2rem;font-weight:700;margin-bottom:1rem}.analysis-card p{font-size:1.1rem;margin-bottom:2rem;opacity:0.9;line-height:1.6}.cta-button{background:white;color:#2563eb;border:none;padding:1rem 2rem;font-size:1.1rem;font-weight:600;border-radius:8px;cursor:pointer;transition:all 0.3s ease;text-transform:uppercase;letter-spacing:0.5px}.cta-button:hover{background:#1d4ed8 !important;color:white !important;transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,0.2)}.analysis-results-section{padding:2rem;border-top:1px solid #e2e8f0}.analysis-results-section h3{color:#059669;font-size:1.5rem;font-weight:600;margin-bottom:1.5rem;text-align:center}.result-buttons-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1rem;margin-bottom:2rem}.result-btn{padding:1rem 1.5rem;font-weight:600;border-radius:8px;text-decoration:none;text-align:center;transition:all 0.3s ease}.result-btn:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,0.15);text-decoration:none}.satisfaction-section{text-align:center;margin-bottom:1.5rem}.satisfaction-label{display:inline-flex;align-items:center;gap:0.5rem;font-size:1.1rem;color:#374151;cursor:pointer}.generate-report-section{text-align:center}.report-btn{padding:1rem 2rem;font-size:1.1rem;font-weight:600;border-radius:8px;border:none;cursor:pointer;transition:all 0.3s ease}.report-btn:hover{transform:translateY(-2px);box-shadow:0 4px 12px rgba(0,0,0,0.15)}.error-message{margin-top:1rem;border-radius:8px}/* Mobile Responsiveness */ @media (max-width:768px){.run-analysis-container{padding:1rem}.analysis-header h1{font-size:2rem}.analysis-workflow{grid-template-columns:1fr}.workflow-step{padding:1rem}.analysis-card{padding:2rem 1rem}.analysis-card h2{font-size:1.5rem}.result-buttons-grid{grid-template-columns:1fr}}@media (max-width:480px){.analysis-header h1{font-size:1.75rem}.analysis-card p{font-size:1rem}.cta-button{padding:0.875rem 1.5rem;font-size:1rem}}