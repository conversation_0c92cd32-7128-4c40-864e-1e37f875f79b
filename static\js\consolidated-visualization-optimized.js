window.DrawingUtils = (function(){'use strict';const CONSTANTS ={BASE_SCALE: 50,BASE_X: 100,BASE_Y: 400,FASCIA_THICKNESS_RATIO: 0.2,COLORS:{FASCIA: '#666',REINFORCED_FILL: '#D6B85A',RETAINED_FILL: '#D2B48C',FOUNDATION_SOIL: '#A98B6D',BACKSLOPE: '#FFA500',TEXT: '#000',DIMENSION_LINE: '#000'},FONTS:{DEFAULT: '18px Arial',DIMENSION: '18px Arial'}};function validateInputs(wallHeight,embedmentDepth,wallLength,wallBatter,backslopeAngle,backslopeRise){const inputs ={wallHeight,embedmentDepth,wallLength,wallBatter,backslopeAngle,backslopeRise};for (const [key,value] of Object.entries(inputs)){if (typeof value !== 'number' || isNaN(value) || value < 0){throw new Error(`Invalid ${key}: ${value}. Must be a non-negative number.`);}}}function toRadians(degrees){return (degrees * Math.PI) / 180;}return{CONSTANTS: CONSTANTS,calculateGeometry: function(wallHeight,embedmentDepth,wallLength,wallBatter,backslopeAngle,backslopeRise){try{validateInputs(wallHeight,embedmentDepth,wallLength,wallBatter,backslopeAngle,backslopeRise);const baseScale = CONSTANTS.BASE_SCALE;const baseX = CONSTANTS.BASE_X;const baseY = CONSTANTS.BASE_Y;const batterOffset = Math.tan(toRadians(wallBatter)) * (wallHeight * baseScale);const fasciaThickness = CONSTANTS.FASCIA_THICKNESS_RATIO * baseScale;return{baseScale,baseX,baseY,batterOffset,fasciaThickness,fascia:{x1: baseX - fasciaThickness,y1: baseY,x2: baseX - fasciaThickness + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + batterOffset + fasciaThickness,y3: baseY - (wallHeight * baseScale),x4: baseX,y4: baseY},reinforcedFill:{x1: baseX,y1: baseY,x2: baseX + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + (wallLength * baseScale) + batterOffset,y3: baseY - (wallHeight * baseScale),x4: baseX + (wallLength * baseScale),y4: baseY},retainedFill:{x1: baseX + (wallLength * baseScale),y1: baseY,x2: baseX + (wallLength * baseScale) + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y3: baseY - (wallHeight * baseScale),x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y4: baseY},embedment:{x1: baseX - 10 - (1 * wallHeight * baseScale),y1: baseY,x2: baseX - 10 + batterOffset,y2: baseY - (embedmentDepth * baseScale)},foundationSoil:{x1: baseX - 10 - (1 * wallHeight * baseScale),y1: baseY + (0.5 * wallHeight * baseScale),x2: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y2: baseY}};}catch (error){console.error('Error calculating geometry:',error);throw error;}},calculateBackslope: function(geometry,backslopeAngle,backslopeRise){try{if (!geometry || typeof geometry !== 'object'){throw new Error('Invalid geometry object provided');}const{reinforcedFill,retainedFill,baseScale}= geometry;const backslopeAngleRadians = toRadians(backslopeAngle);if (backslopeAngle === 0 || Math.tan(backslopeAngleRadians) === 0){return{slopeStartX: reinforcedFill.x2,slopeStartY: reinforcedFill.y2,slopeEndX: retainedFill.x3,slopeEndY: reinforcedFill.y2,isCase1: true,backslopeAngleRadians: 0};}const slopeBaseLength = backslopeRise / Math.tan(backslopeAngleRadians);const slopeStartX = reinforcedFill.x2;const slopeStartY = reinforcedFill.y2;let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);let slopeEndY = slopeStartY - (backslopeRise * baseScale);const isCase1 = slopeEndX <= retainedFill.x3;if (!isCase1){slopeEndX = retainedFill.x3;slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));}return{slopeStartX,slopeStartY,slopeEndX,slopeEndY,isCase1,backslopeAngleRadians};}catch (error){console.error('Error calculating backslope:',error);throw error;}},drawBasicWall: function(ctx,geometry,slope){try{if (!ctx || !geometry || !slope){throw new Error('Missing required parameters for drawBasicWall');}const{fascia,reinforcedFill,retainedFill,embedment,foundationSoil}= geometry;const{slopeStartX,slopeStartY,slopeEndX,slopeEndY,isCase1}= slope;ctx.fillStyle = CONSTANTS.COLORS.FOUNDATION_SOIL;ctx.fillRect(foundationSoil.x1,foundationSoil.y1,foundationSoil.x2 - foundationSoil.x1,foundationSoil.y2 - foundationSoil.y1);ctx.fillStyle = CONSTANTS.COLORS.FOUNDATION_SOIL;ctx.fillRect(embedment.x1,embedment.y1,embedment.x2 - embedment.x1,embedment.y2 - embedment.y1);ctx.fillStyle = CONSTANTS.COLORS.FASCIA;ctx.beginPath();ctx.moveTo(fascia.x1,fascia.y1);ctx.lineTo(fascia.x2,fascia.y2);ctx.lineTo(fascia.x3,fascia.y3);ctx.lineTo(fascia.x4,fascia.y4);ctx.closePath();ctx.fill();ctx.fillStyle = CONSTANTS.COLORS.REINFORCED_FILL;ctx.beginPath();ctx.moveTo(reinforcedFill.x1,reinforcedFill.y1);ctx.lineTo(reinforcedFill.x2,reinforcedFill.y2);ctx.lineTo(reinforcedFill.x3,reinforcedFill.y3);ctx.lineTo(reinforcedFill.x4,reinforcedFill.y4);ctx.closePath();ctx.fill();ctx.fillStyle = CONSTANTS.COLORS.RETAINED_FILL;ctx.beginPath();ctx.moveTo(retainedFill.x1,retainedFill.y1);ctx.lineTo(retainedFill.x2,retainedFill.y2);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x4,retainedFill.y4);ctx.closePath();ctx.fill();this._drawBackslope(ctx,slope,retainedFill);}catch (error){console.error('Error drawing basic wall:',error);throw error;}},_drawBackslope: function(ctx,slope,retainedFill){const{slopeStartX,slopeStartY,slopeEndX,slopeEndY,isCase1}= slope;ctx.fillStyle = CONSTANTS.COLORS.BACKSLOPE;ctx.strokeStyle = CONSTANTS.COLORS.DIMENSION_LINE;ctx.lineWidth = 1;if (isCase1){const horizontalEndX = retainedFill.x3;const horizontalEndY = slopeEndY;ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeEndX,slopeEndY);ctx.lineTo(horizontalEndX,horizontalEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.lineTo(horizontalEndX,horizontalEndY);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.closePath();ctx.fill();}else{ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.closePath();ctx.fill();}},drawDimensions: function(ctx,geometry,wallHeight,wallLength,embedmentDepth){try{if (!ctx || !geometry){throw new Error('Missing required parameters for drawDimensions');}const{baseX,baseY,baseScale,fasciaThickness}= geometry;ctx.strokeStyle = CONSTANTS.COLORS.DIMENSION_LINE;ctx.lineWidth = 1;ctx.font = CONSTANTS.FONTS.DIMENSION;ctx.fillStyle = CONSTANTS.COLORS.TEXT;ctx.beginPath();ctx.moveTo(baseX - 20,baseY);ctx.lineTo(baseX - 20,baseY - (wallHeight * baseScale));ctx.stroke();this.drawArrow(ctx,baseX - 20,baseY,baseX - 20,baseY - (wallHeight * baseScale),0.05);ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${wallHeight}m`,baseX - 40,baseY - (0.5 * wallHeight * baseScale) - 10);ctx.beginPath();ctx.moveTo(baseX,baseY);ctx.lineTo(baseX + (wallLength * baseScale),baseY);ctx.stroke();this.drawArrow(ctx,baseX,baseY,baseX + (wallLength * baseScale),baseY,0.05);ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(`${wallLength}m`,baseX + ((wallLength * baseScale) / 2),baseY + 10);ctx.beginPath();ctx.moveTo(baseX - 40,baseY);ctx.lineTo(baseX - 40,baseY - (embedmentDepth * baseScale));ctx.stroke();this.drawArrow(ctx,baseX - 40,baseY,baseX - 40,baseY - (embedmentDepth * baseScale),0.2);ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${embedmentDepth}m`,baseX - 50 - fasciaThickness,baseY - (0.5 * embedmentDepth * baseScale) - 10);}catch (error){console.error('Error drawing dimensions:',error);throw error;}},drawArrow: function(ctx,x1,y1,x2,y2,arrowSize = 0.05){try{if (!ctx){throw new Error('Canvas context is required');}ctx.strokeStyle = CONSTANTS.COLORS.DIMENSION_LINE;ctx.fillStyle = CONSTANTS.COLORS.DIMENSION_LINE;const dx = x2 - x1;const dy = y2 - y1;const angle = Math.atan2(dy,dx);const arrowLength = Math.sqrt(dx * dx + dy * dy) * arrowSize;ctx.save();ctx.translate(x2,y2);ctx.rotate(angle);ctx.beginPath();ctx.moveTo(0,0);ctx.lineTo(-arrowLength,-arrowLength / 2);ctx.lineTo(-arrowLength,arrowLength / 2);ctx.closePath();ctx.fill();ctx.restore();ctx.save();ctx.translate(x1,y1);ctx.rotate(angle + Math.PI);ctx.beginPath();ctx.moveTo(0,0);ctx.lineTo(-arrowLength,-arrowLength / 2);ctx.lineTo(-arrowLength,arrowLength / 2);ctx.closePath();ctx.fill();ctx.restore();ctx.beginPath();ctx.moveTo(x1,y1);ctx.lineTo(x2,y2);ctx.stroke();}catch (error){console.error('Error drawing arrow:',error);throw error;}},drawLabels: function(ctx,geometry,wallLength){try{if (!ctx || !geometry){throw new Error('Missing required parameters for drawLabels');}const{baseX,baseY,baseScale}= geometry;ctx.font = CONSTANTS.FONTS.DEFAULT;ctx.fillStyle = CONSTANTS.COLORS.TEXT;ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(`Reinforced Fill`,baseX + ((0.5 * wallLength * baseScale)),baseY - (0.5 * wallLength * baseScale) - 10);ctx.fillText(`Retained Fill`,baseX + ((1.5 * wallLength * baseScale)),baseY - (0.5 * wallLength * baseScale) - 10);ctx.fillText(`Foundation Soil`,baseX + ((1.0 * wallLength * baseScale)),baseY + (0.25 * wallLength * baseScale) - 10);}catch (error){console.error('Error drawing labels:',error);throw error;}},drawLoadArrow: function(ctx,fromX,fromY,toX,toY,size,color,hollow = false){try{if (!ctx){throw new Error('Canvas context is required');}const angle = Math.atan2(toY - fromY,toX - fromX);ctx.strokeStyle = color;ctx.lineWidth = 2;ctx.beginPath();ctx.moveTo(fromX,fromY);ctx.lineTo(toX,toY);if (hollow){ctx.stroke();}else{ctx.lineTo(toX - size * Math.cos(angle - Math.PI / 6),toY - size * Math.sin(angle - Math.PI / 6));ctx.moveTo(toX,toY);ctx.lineTo(toX - size * Math.cos(angle + Math.PI / 6),toY - size * Math.sin(angle + Math.PI / 6));ctx.stroke();}}catch (error){console.error('Error drawing load arrow:',error);throw error;}}};})();if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost'){console.log('✅ Drawing utilities loaded (development mode)');}(function(){'use strict';if (typeof window.DrawingUtils === 'undefined'){console.error('DrawingUtils is required but not loaded. Please ensure drawingUtils.js is loaded first.');return;}function debounce(func,wait){let timeout;return function executedFunction(...args){const later = () =>{clearTimeout(timeout);func.apply(this,args);};clearTimeout(timeout);timeout = setTimeout(later,wait);};}const domCache = new Map();function getCachedElement(id){if (!domCache.has(id)){domCache.set(id,document.getElementById(id));}return domCache.get(id);}function clearDOMCache(){domCache.clear();}let eventListeners = [];let timeouts = [];let intervals = [];function addEventListenerTracked(element,event,handler,options){element.addEventListener(event,handler,options);eventListeners.push({element,event,handler,options});}function setTimeoutTracked(callback,delay){const id = setTimeout(callback,delay);timeouts.push(id);return id;}function cleanup(){eventListeners.forEach(({element,event,handler,options}) =>{if (element && element.removeEventListener){element.removeEventListener(event,handler,options);}});eventListeners = [];timeouts.forEach(id => clearTimeout(id));timeouts = [];intervals.forEach(id => clearInterval(id));intervals = [];clearDOMCache();window.geometryVisualizationInitialized = false;}window.geometryVisualizationCleanup = cleanup;function initializeGeometryVisualization(){if (window.geometryVisualizationInitialized){return false;}try{const canvas = getCachedElement('geometry-canvas');if (!canvas){return false;}const ctx = canvas.getContext('2d');if (!ctx){return false;}let scale = 1;let translateX = 0;let translateY = 0;let isDown = false;let lastX,lastY;canvas.width = 1000;canvas.height = 600;function getFormValues(){return{wallHeight: parseFloat(document.getElementById('wall-height')?.value) || 5,embedmentDepth: parseFloat(document.getElementById('embedment-depth')?.value) || 1,wallLength: parseFloat(document.getElementById('wall-length')?.value) || 6,wallBatter: parseFloat(document.getElementById('wall-batter')?.value) || 0,backslopeAngle: parseFloat(document.getElementById('backslope-angle')?.value) || 0,backslopeRise: parseFloat(document.getElementById('backslope-rise')?.value) || 0};}function drawGRSWall(wallHeight,embedmentDepth,wallLength,wallBatter,backslopeAngle,backslopeRise){try{ctx.clearRect(0,0,canvas.width,canvas.height);ctx.save();ctx.translate(translateX,translateY);ctx.scale(scale,scale);const baseScale = 50;const baseX = 100,baseY = 400;const batterOffset = Math.tan((wallBatter * Math.PI) / 180) * (wallHeight * baseScale);const fasciaThickness = 0.2 * baseScale;const fascia ={x1: baseX - fasciaThickness,y1: baseY,x2: baseX - fasciaThickness + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + batterOffset + fasciaThickness,y3: baseY - (wallHeight * baseScale),x4: baseX,y4: baseY};const reinforcedFill ={x1: baseX,y1: baseY,x2: baseX + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + (wallLength * baseScale) + batterOffset,y3: baseY - (wallHeight * baseScale),x4: baseX + (wallLength * baseScale),y4: baseY};const retainedFill ={x1: baseX + (wallLength * baseScale),y1: baseY,x2: baseX + (wallLength * baseScale) + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y3: baseY - (wallHeight * baseScale),x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y4: baseY};const embedment ={x1: baseX - 10 - (1 * wallHeight * baseScale),y1: baseY,x2: baseX - 10 + batterOffset,y2: baseY - (embedmentDepth * baseScale)};const foundationSoil ={x1: embedment.x1,y1: baseY + (0.5*wallHeight * baseScale),x2: retainedFill.x4,y2: baseY};const backslopeAngleRadians = (backslopeAngle * Math.PI) / 180;const slopeBaseLength = backslopeRise / Math.tan(backslopeAngleRadians);const slopeStartX = reinforcedFill.x2;const slopeStartY = reinforcedFill.y2;let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);let slopeEndY = slopeStartY - (backslopeRise * baseScale);ctx.fillStyle = "#FFA500";if (slopeEndX <= retainedFill.x3){const horizontalEndX = retainedFill.x3;const horizontalEndY = slopeEndY;ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeEndX,slopeEndY);ctx.lineTo(horizontalEndX,horizontalEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.lineTo(horizontalEndX,horizontalEndY);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.closePath();ctx.fill();const topRise = backslopeRise;ctx.beginPath();ctx.moveTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x3,retainedFill.y3 - topRise * baseScale);ctx.stroke();window.DrawingUtils.drawArrow(ctx,retainedFill.x3,retainedFill.y3,retainedFill.x3,retainedFill.y3 - topRise * baseScale,0.1);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.fillText(`${topRise.toFixed(2)}m`,retainedFill.x3 -40,retainedFill.y3 - (topRise * baseScale) / 2);}else{slopeEndX = retainedFill.x3;slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.closePath();ctx.fill();const topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan(backslopeAngleRadians)/baseScale;ctx.beginPath();ctx.moveTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x3,retainedFill.y3 - topRise*baseScale);ctx.stroke();window.DrawingUtils.drawArrow(ctx,retainedFill.x3,retainedFill.y3,retainedFill.x3,retainedFill.y3 - (topRise*baseScale),0.1);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.fillText(`${topRise.toFixed(2)}m`,retainedFill.x3 - 40,retainedFill.y3 - (topRise*0.5*baseScale));}ctx.fillStyle = "#A98B6D";ctx.fillRect(foundationSoil.x1,foundationSoil.y1,foundationSoil.x2 - foundationSoil.x1,foundationSoil.y2 - foundationSoil.y1);ctx.fillRect(embedment.x1,embedment.y1,embedment.x2 - embedment.x1,embedment.y2 - embedment.y1);ctx.fillStyle = "#666";ctx.beginPath();ctx.moveTo(fascia.x1,fascia.y1);ctx.lineTo(fascia.x2,fascia.y2);ctx.lineTo(fascia.x3,fascia.y3);ctx.lineTo(fascia.x4,fascia.y4);ctx.fill();ctx.fillStyle = "#D6B85A";ctx.beginPath();ctx.moveTo(reinforcedFill.x1,reinforcedFill.y1);ctx.lineTo(reinforcedFill.x2,reinforcedFill.y2);ctx.lineTo(reinforcedFill.x3,reinforcedFill.y3);ctx.lineTo(reinforcedFill.x4,reinforcedFill.y4);ctx.fill();ctx.fillStyle = "#D2B48C";ctx.beginPath();ctx.moveTo(retainedFill.x1,retainedFill.y1);ctx.lineTo(retainedFill.x2,retainedFill.y2);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x4,retainedFill.y4);ctx.fill();ctx.strokeStyle = "#000";ctx.lineWidth = 1;ctx.beginPath();ctx.moveTo(baseX - 20,baseY);ctx.lineTo(baseX - 20,baseY - (wallHeight * baseScale));ctx.stroke();window.DrawingUtils.drawArrow(ctx,baseX - 20,baseY,baseX - 20,baseY - (wallHeight * baseScale),0.05);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${wallHeight}m`,baseX - 40,baseY - (0.5*wallHeight * baseScale) - 10);ctx.beginPath();ctx.moveTo(baseX,baseY);ctx.lineTo(baseX + (wallLength * baseScale),baseY);ctx.stroke();window.DrawingUtils.drawArrow(ctx,baseX,baseY,baseX + (wallLength * baseScale),baseY,0.05);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(`${wallLength}m`,baseX + ((wallLength * baseScale) / 2),baseY + 10);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(`Reinforced Fill`,baseX + ((0.5*wallLength * baseScale)),baseY - (0.5*wallHeight * baseScale) - 10);ctx.fillText(`Retained Fill`,baseX + ((1.5*wallLength * baseScale)),baseY - (0.5*wallHeight * baseScale) - 10);ctx.fillText(`Foundation Soil`,baseX + ((1.0*wallLength * baseScale)),baseY + (0.25*wallHeight * baseScale) - 10);ctx.beginPath();ctx.moveTo(baseX - 40,baseY);ctx.lineTo(baseX - 40,baseY - (embedmentDepth * baseScale));ctx.stroke();window.DrawingUtils.drawArrow(ctx,baseX - 40,baseY,baseX - 40,baseY - (embedmentDepth * baseScale),0.2);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${embedmentDepth}m`,baseX - 50-fasciaThickness,baseY - (0.5*embedmentDepth * baseScale) - 10);ctx.restore();}catch (error){console.error('Error drawing GRS wall:',error);ctx.restore();}}function redrawCanvas(){try{const values = getFormValues();drawGRSWall(
values.wallHeight,values.embedmentDepth,values.wallLength,values.wallBatter,values.backslopeAngle,values.backslopeRise
);}catch (error){console.error('Error redrawing geometry canvas:',error);}}const debouncedRedraw = debounce(() =>{if (typeof redrawCanvas === 'function'){redrawCanvas();}},150);function handleGeometryInputChange(){if (window.location.hostname === 'localhost'){console.log("Geometry input changed:",this.id,"value:",this.value);}if (typeof validateField === 'function'){validateField(this);}debouncedRedraw();}function setupFormListeners(){if (window.location.hostname === 'localhost'){console.log("Setting up event delegation for geometry form...");}addEventListenerTracked(document,'input',handleDelegatedInput);addEventListenerTracked(document,'change',handleDelegatedInput);if (window.location.hostname === 'localhost'){console.log("Event delegation setup complete for geometry inputs");}return true;}function handleDelegatedInput(event){const target = event.target;if (!target || target.type !== 'number') return;const geometryForm = getCachedElement('geometry-form');if (!geometryForm || !geometryForm.contains(target)) return;if (window.location.hostname === 'localhost'){console.log("Delegated geometry input changed:",target.id,"value:",target.value);}handleGeometryInputChange.call(target);}setupFormListeners();window.geometryRedrawCanvas = redrawCanvas;if (canvas){canvas.redrawCanvas = redrawCanvas;}const zoomInButton = document.getElementById('zoom-in-button');const zoomOutButton = document.getElementById('zoom-out-button');if (zoomInButton && zoomOutButton){zoomInButton.addEventListener('click',function(){const cursorX = canvas.width / 2;const cursorY = canvas.height / 2;const oldScale = scale;scale *= 1.1;translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);redrawCanvas();});zoomOutButton.addEventListener('click',function(){const cursorX = canvas.width / 2;const cursorY = canvas.height / 2;const oldScale = scale;scale *= 0.9;translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);redrawCanvas();});}const fitButton = document.getElementById('fit-button');if (fitButton){fitButton.addEventListener('click',function(){scale = 1;translateX = 0;translateY = 0;redrawCanvas();});}canvas.addEventListener('wheel',function(e){e.preventDefault();const rect = canvas.getBoundingClientRect();const cursorX = e.clientX - rect.left;const cursorY = e.clientY - rect.top;const oldScale = scale;scale *= e.deltaY > 0 ? 0.9 : 1.1;translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);redrawCanvas();});canvas.addEventListener('mousedown',function(e){isDown = true;canvas.style.cursor = 'grabbing';const rect = canvas.getBoundingClientRect();lastX = e.clientX - rect.left;lastY = e.clientY - rect.top;});canvas.addEventListener('mouseup',function(){isDown = false;canvas.style.cursor = 'grab';});canvas.addEventListener('mousemove',function(e){if (isDown){const rect = canvas.getBoundingClientRect();const currentX = e.clientX - rect.left;const currentY = e.clientY - rect.top;translateX += currentX - lastX;translateY += currentY - lastY;lastX = currentX;lastY = currentY;redrawCanvas();}});setTimeout(() =>{redrawCanvas();},100);setTimeout(() =>{redrawCanvas();},600);window.geometryVisualizationInitialized = true;return true;}catch (error){console.error('Error initializing geometry visualization:',error);return false;}}function setupScreenshotFunctionality(){const screenshotButton = document.getElementById('screenshot-button');const geometryCanvas = document.getElementById('geometry-canvas');const geometryForm = document.getElementById('geometry-form');if (screenshotButton && geometryCanvas && geometryForm){const newScreenshotButton = screenshotButton.cloneNode(true);screenshotButton.parentNode.replaceChild(newScreenshotButton,screenshotButton);newScreenshotButton.addEventListener('click',handleGeometryScreenshot);}}function handleGeometryScreenshot(){try{const geometryForm = document.getElementById('geometry-form');if (!geometryForm) return;const canvas = document.getElementById('geometry-canvas');if (!canvas) return;const dataURL = canvas.toDataURL('image/png');storeScreenshotOnServer(dataURL);const link = document.createElement('a');link.href = dataURL;link.download = 'grs_wall_geometry.png';link.click();}catch (error){console.error('Error taking geometry screenshot:',error);}}function storeScreenshotOnServer(dataURL){fetch('/store-screenshot',{method: 'POST',headers:{'Content-Type': 'application/json',},body: JSON.stringify({screenshot: dataURL})})
.then(response => response.json())
.then(data =>{if (data.status !== 'success'){console.error('Failed to store screenshot:',data.message);}})
.catch(error =>{console.error('Error storing screenshot:',error);});}setupScreenshotFunctionality();function checkGeometryElementsReady(){const canvas = document.getElementById('geometry-canvas');const form = document.getElementById('geometry-form');const wallHeightInput = document.getElementById('wall-height');return canvas && form && wallHeightInput;}function waitForGeometryElements(){if (checkGeometryElementsReady()){initializeGeometryVisualization();}else{setTimeout(waitForGeometryElements,50);}}window.initializeGeometryVisualization = initializeGeometryVisualization;if (document.readyState === 'loading'){document.addEventListener('DOMContentLoaded',waitForGeometryElements);}else{waitForGeometryElements();}function retryInitialization(attempts = 0,maxAttempts = 5){if (attempts >= maxAttempts){if (window.location.hostname === 'localhost'){console.warn('Geometry visualization initialization failed after',maxAttempts,'attempts');}return;}if (getCachedElement('geometry-canvas') && !window.geometryVisualizationInitialized){initializeGeometryVisualization();}else if (!window.geometryVisualizationInitialized){setTimeoutTracked(() =>{retryInitialization(attempts + 1,maxAttempts);},100 * (attempts + 1));}}retryInitialization();addEventListenerTracked(window,'load',() =>{if (getCachedElement('geometry-canvas') && !window.geometryVisualizationInitialized){initializeGeometryVisualization();}});})();(function(){'use strict';if (typeof window.DrawingUtils === 'undefined'){console.error('DrawingUtils is required but not loaded. Please ensure drawingUtils.js is loaded first.');return;}const domCache = new Map();function getCachedElement(id){if (!domCache.has(id)){domCache.set(id,document.getElementById(id));}return domCache.get(id);}let eventListeners = [];let timeouts = [];function addEventListenerTracked(element,event,handler,options){element.addEventListener(event,handler,options);eventListeners.push({element,event,handler,options});}function setTimeoutTracked(callback,delay){const id = setTimeout(callback,delay);timeouts.push(id);return id;}function cleanup(){eventListeners.forEach(({element,event,handler,options}) =>{if (element && element.removeEventListener){element.removeEventListener(event,handler,options);}});eventListeners = [];timeouts.forEach(id => clearTimeout(id));timeouts = [];domCache.clear();}window.externalLoadsVisualizationCleanup = cleanup;function initializeExternalLoadsVisualization(){try{const reinforcementLayoutTable = document.getElementById('reinforcementLayoutTable');if (reinforcementLayoutTable){return false;}const{geometryData,externalloads_data}= loadStoredData();const geometryParams = extractGeometryParams(geometryData);const loadParams = extractLoadParams(externalloads_data);const{canvas,ctx}= setupCanvas();if (!canvas || !ctx){return false;}const visualizationState = initializeVisualizationState(canvas,ctx,geometryParams,loadParams);setupEventHandlers(visualizationState);performInitialDraw(visualizationState);return true;}catch (error){console.error('Error initializing external loads visualization:',error);return false;}}function loadStoredData(){let geometryData ={};let externalloads_data ={};try{geometryData = JSON.parse(localStorage.getItem('geometryData')) ||{};externalloads_data = JSON.parse(localStorage.getItem('externalloads_data')) ||{};}catch (error){console.error('Error parsing localStorage data:',error);}return{geometryData,externalloads_data};}function extractGeometryParams(geometryData){return{wallHeight: geometryData.wallHeight || 5,embedmentDepth: geometryData.embedmentDepth || 1,wallLength: geometryData.wallLength || 6,wallBatter: geometryData.wallBatter || 0,backslopeAngle: geometryData.backslopeAngle || 0,backslopeRise: geometryData.backslopeRise || 2};}function extractLoadParams(externalloads_data){return{dead_loads: externalloads_data.dead_loads || [0,0,0],live_loads: externalloads_data.live_loads || [0,0,0],vertical_strip_load: externalloads_data.vertical_strip_load || [0,0,0],horizontal_strip_load: externalloads_data.horizontal_strip_load || [0,0,0],strip_load_width: externalloads_data.strip_load_width || [0,0,0],strip_load_distance: externalloads_data.strip_load_distance || [0,0,0],earthquake_acceleration: externalloads_data.earthquake_acceleration || 0,seismic_force: externalloads_data.seismic_force || 0,impact_loads: externalloads_data.impact_loads ||{rupture:{upper: 0,second: 0},pullout:{upper: 0,second: 0}}};}function setupCanvas(){const canvas = document.getElementById('geometry2-canvas');if (!canvas){return{canvas: null,ctx: null};}const ctx = canvas.getContext('2d');if (!ctx){return{canvas,ctx: null};}canvas.width = 1000;canvas.height = 600;return{canvas,ctx};}function initializeVisualizationState(canvas,ctx,geometryParams,loadParams){return{canvas,ctx,geometryParams,loadParams,scale: 1,translateX: 0,translateY: 0,isDown: false,lastX: 0,lastY: 0,geometry: null,slope: null};}function drawGRSWallWithLoads(state){try{const{canvas,ctx,scale,translateX,translateY}= state;const{geometryData}= loadStoredData();const geometryParams = extractGeometryParams(geometryData);const loadParams = getCurrentFormValues();state.geometryParams = geometryParams;state.loadParams = loadParams;ctx.clearRect(0,0,canvas.width,canvas.height);ctx.save();ctx.translate(translateX,translateY);ctx.scale(scale,scale);const geometry = window.DrawingUtils.calculateGeometry(
geometryParams.wallHeight,geometryParams.embedmentDepth,geometryParams.wallLength,geometryParams.wallBatter,geometryParams.backslopeAngle,geometryParams.backslopeRise
);const slope = window.DrawingUtils.calculateBackslope(
geometry,geometryParams.backslopeAngle,geometryParams.backslopeRise
);state.geometry = geometry;state.slope = slope;window.DrawingUtils.drawBasicWall(ctx,geometry,slope);window.DrawingUtils.drawDimensions(
ctx,geometry,geometryParams.wallHeight,geometryParams.wallLength,geometryParams.embedmentDepth
);window.DrawingUtils.drawLabels(ctx,geometry,geometryParams.wallLength);drawBackslopeRiseDimension(ctx,geometry,slope,geometryParams);drawExternalLoads(ctx,geometry,slope,geometryParams,loadParams);ctx.restore();}catch (error){console.error('Error drawing GRS wall with loads:',error);}}function drawBackslopeRiseDimension(ctx,geometry,slope,geometryParams){try{const{retainedFill,baseScale,reinforcedFill}= geometry;const{isCase1}= slope;const{backslopeAngle,backslopeRise}= geometryParams;if (backslopeAngle <= 0){return;}let topRise;if (isCase1){topRise = backslopeRise;}else{topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan((backslopeAngle * Math.PI) / 180) / baseScale;}if (topRise <= 0){return;}ctx.strokeStyle = window.DrawingUtils.CONSTANTS.COLORS.DIMENSION_LINE;ctx.lineWidth = 1;ctx.beginPath();ctx.moveTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x3,retainedFill.y3 - topRise * baseScale);ctx.stroke();window.DrawingUtils.drawArrow(
ctx,retainedFill.x3,retainedFill.y3,retainedFill.x3,retainedFill.y3 - topRise * baseScale,0.1
);ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;ctx.fillStyle = window.DrawingUtils.CONSTANTS.COLORS.TEXT;ctx.textAlign = "center";ctx.fillText(
`${topRise.toFixed(2)}m`,retainedFill.x3 - 40,retainedFill.y3 - (topRise * baseScale) / 2
);}catch (error){console.error('Error drawing backslope rise dimension:',error);}}function drawExternalLoads(ctx,geometry,slope,geometryParams,loadParams){try{const{reinforcedFill,retainedFill}= geometry;const{slopeEndX,slopeEndY,isCase1}= slope;const{backslopeAngle}= geometryParams;const{dead_loads,live_loads,vertical_strip_load,horizontal_strip_load,strip_load_width,strip_load_distance,earthquake_acceleration}= loadParams;if (backslopeAngle === 0){const loadY = reinforcedFill.y2 - 50;drawUniformLoads(ctx,reinforcedFill.x2,retainedFill.x3,loadY,dead_loads,"red","Dead Load");drawUniformLoads(ctx,reinforcedFill.x2,retainedFill.x3,loadY - 50,live_loads,"blue","Live Load");}else if (isCase1){const loadY = slopeEndY - 50;drawUniformLoads(ctx,slopeEndX,retainedFill.x3,loadY,dead_loads,"red","Dead Load");drawUniformLoads(ctx,slopeEndX,retainedFill.x3,loadY - 50,live_loads,"blue","Live Load");}else{ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;ctx.fillStyle = window.DrawingUtils.CONSTANTS.COLORS.TEXT;ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(
"Dead and Live Loads: Beyond Influence Zone",(reinforcedFill.x2 + retainedFill.x3) / 2,reinforcedFill.y2 - 50
);}const stripLoadY = (backslopeAngle === 0 ? reinforcedFill.y2 : slopeEndY) - 20;drawStripLoads(ctx,geometry,stripLoadY,vertical_strip_load,horizontal_strip_load,strip_load_width,strip_load_distance);drawEarthquakeAcceleration(ctx,geometry,earthquake_acceleration);}catch (error){console.error('Error drawing external loads:',error);}}function drawUniformLoads(ctx,startX,endX,y,loadValues,color,label){try{const totalLoad = loadValues.reduce((a,b) => a + b,0);ctx.strokeStyle = color;ctx.lineWidth = 2;ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;ctx.fillStyle = color;ctx.textAlign = "center";ctx.textBaseline = "top";const arrowSpacing = 50;for (let x = startX;x <= endX;x += arrowSpacing){window.DrawingUtils.drawLoadArrow(ctx,x,y,x,y + 20,5,color,false);}ctx.fillText(`${label}: ${totalLoad.toFixed(2)}kPa`,(startX + endX) / 2,y - 25);}catch (error){console.error('Error drawing uniform loads:',error);}}function drawStripLoads(ctx,geometry,y,verticalStripLoads,horizontalStripLoads,stripLoadWidths,stripLoadDistances){try{const stripLoadColor = "purple";const{reinforcedFill,retainedFill,baseScale}= geometry;for (let i = 0;i < verticalStripLoads.length;i++){if (verticalStripLoads[i] > 0 || horizontalStripLoads[i] > 0){const stripLoadX = reinforcedFill.x2 + (stripLoadDistances[i] * baseScale) - (stripLoadWidths[i] * baseScale / 2);const centerX = stripLoadX + (stripLoadWidths[i] * baseScale / 2);if (stripLoadX <= retainedFill.x3){ctx.fillStyle = stripLoadColor;ctx.fillRect(stripLoadX,y,stripLoadWidths[i] * baseScale,20);if (verticalStripLoads[i] > 0){window.DrawingUtils.drawLoadArrow(ctx,centerX,y - 40,centerX,y,5,stripLoadColor,false);ctx.font = "14px Arial";ctx.fillStyle = stripLoadColor;ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${verticalStripLoads[i].toFixed(2)}kn/m`,centerX,y - 30);}if (horizontalStripLoads[i] > 0){window.DrawingUtils.drawLoadArrow(ctx,centerX,y - 5,centerX - 30,y - 5,5,stripLoadColor,false);ctx.font = "14px Arial";ctx.fillStyle = stripLoadColor;ctx.textAlign = "right";ctx.textBaseline = "middle";ctx.fillText(`${horizontalStripLoads[i].toFixed(2)}kn/m`,centerX - 35,y);}}else{ctx.font = "14px Arial";ctx.fillStyle = stripLoadColor;ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`Strip Load ${i + 1}: Beyond Influence Zone`,retainedFill.x3 + 50,y - 40);}}}}catch (error){console.error('Error drawing strip loads:',error);}}function drawEarthquakeAcceleration(ctx,geometry,acceleration){try{const{retainedFill}= geometry;const x = (retainedFill.x1 + retainedFill.x3) / 2;const y = (retainedFill.y1 + retainedFill.y3 + 60) / 2;const arrowLength = 50;const arrowSize = 10;const label = `${acceleration.toFixed(2)}g`;ctx.strokeStyle = window.DrawingUtils.CONSTANTS.COLORS.DIMENSION_LINE;ctx.lineWidth = 2;ctx.beginPath();ctx.moveTo(x,y);ctx.lineTo(x - arrowLength,y);ctx.lineTo(x - arrowLength + arrowSize,y - arrowSize / 2);ctx.moveTo(x - arrowLength,y);ctx.lineTo(x - arrowLength + arrowSize,y + arrowSize / 2);ctx.stroke();ctx.font = window.DrawingUtils.CONSTANTS.FONTS.DEFAULT;ctx.fillStyle = window.DrawingUtils.CONSTANTS.COLORS.TEXT;ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(label,x - arrowLength - 30,y);}catch (error){console.error('Error drawing earthquake acceleration:',error);}}function setupEventHandlers(state){const{canvas}= state;canvas.addEventListener('wheel',function(e){e.preventDefault();const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;state.scale *= zoomFactor;drawGRSWallWithLoads(state);});canvas.addEventListener('mousedown',function(e){state.isDown = true;const rect = canvas.getBoundingClientRect();state.lastX = e.clientX - rect.left;state.lastY = e.clientY - rect.top;});canvas.addEventListener('mousemove',function(e){if (!state.isDown) return;const rect = canvas.getBoundingClientRect();const currentX = e.clientX - rect.left;const currentY = e.clientY - rect.top;state.translateX += currentX - state.lastX;state.translateY += currentY - state.lastY;state.lastX = currentX;state.lastY = currentY;drawGRSWallWithLoads(state);});canvas.addEventListener('mouseup',function(){state.isDown = false;});canvas.addEventListener('mouseleave',function(){state.isDown = false;});setupDynamicInputListeners(state);setupScreenshotFunctionality();setupZoomButtons(state);}function setupDynamicInputListeners(state){const externalLoadsForm = document.getElementById('externalloadsform');if (!externalLoadsForm){return;}const inputFields = externalLoadsForm.querySelectorAll('input[type="number"],input[type="checkbox"]');if (inputFields) {inputFields.forEach(input =>{input.removeEventListener('input',handleInputChange);input.addEventListener('input',handleInputChange);input.removeEventListener('change',handleInputChange);input.addEventListener('change',handleInputChange);});}function handleInputChange(){const input = this;if (typeof validateField === 'function'){validateField(input);}updateLoadParameter(state);setTimeout(() =>{drawGRSWallWithLoads(state);},10);}}function getCurrentFormValues(){const loadParams ={dead_loads: [0,0,0,0,0],live_loads: [0,0,0,0,0],vertical_strip_load: [0,0,0,0,0],horizontal_strip_load: [0,0,0,0,0],strip_load_width: [0,0,0,0,0],strip_load_distance: [0,0,0,0,0],earthquake_acceleration: 0,seismic_force: 0,impact_loads:{rupture:{},pullout:{}}};for (let i = 1;i <= 5;i++){const input = document.getElementById(`dead_load${i}`);if (input) loadParams.dead_loads[i-1] = parseFloat(input.value) || 0;}for (let i = 1;i <= 5;i++){const input = document.getElementById(`live_load${i}`);if (input) loadParams.live_loads[i-1] = parseFloat(input.value) || 0;}for (let i = 1;i <= 5;i++){const input = document.getElementById(`vertical_strip_load${i}`);if (input) loadParams.vertical_strip_load[i-1] = parseFloat(input.value) || 0;}for (let i = 1;i <= 5;i++){const input = document.getElementById(`horizontal_strip_load${i}`);if (input) loadParams.horizontal_strip_load[i-1] = parseFloat(input.value) || 0;}for (let i = 1;i <= 5;i++){const input = document.getElementById(`strip_load_width${i}`);if (input) loadParams.strip_load_width[i-1] = parseFloat(input.value) || 0;}for (let i = 1;i <= 5;i++){const input = document.getElementById(`strip_load_distance${i}`);if (input) loadParams.strip_load_distance[i-1] = parseFloat(input.value) || 0;}const eqInput = document.getElementById('earthquake_acceleration');if (eqInput) loadParams.earthquake_acceleration = parseFloat(eqInput.value) || 0;const seismicInput = document.getElementById('seismic_force');if (seismicInput) loadParams.seismic_force = parseFloat(seismicInput.value) || 0;const impactInputs = document.querySelectorAll('input[id*="impact_"]');if (impactInputs) {impactInputs.forEach(input =>{const parts = input.id.split('_');if (parts.length >= 3){const type = parts[1];const layer = parts[2];if (!loadParams.impact_loads[type]) loadParams.impact_loads[type] ={};loadParams.impact_loads[type][layer] = parseFloat(input.value) || 0;}});}return loadParams;}function updateLoadParameter(state){state.loadParams = getCurrentFormValues();}function setupZoomButtons(state){const zoomInButton = document.getElementById('zoom-in-button');const zoomOutButton = document.getElementById('zoom-out-button');const fitButton = document.getElementById('fit-button');if (zoomInButton){zoomInButton.addEventListener('click',function(){const cursorX = state.canvas.width / 2;const cursorY = state.canvas.height / 2;const oldScale = state.scale;state.scale *= 1.1;state.translateX = state.translateX + (cursorX - state.translateX - cursorX / oldScale) * (1 - 1 / oldScale);state.translateY = state.translateY + (cursorY - state.translateY - cursorY / oldScale) * (1 - 1 / oldScale);drawGRSWallWithLoads(state);});}if (zoomOutButton){zoomOutButton.addEventListener('click',function(){const cursorX = state.canvas.width / 2;const cursorY = state.canvas.height / 2;const oldScale = state.scale;state.scale *= 0.9;state.translateX = state.translateX + (cursorX - state.translateX - cursorX / oldScale) * (1 - 1 / oldScale);state.translateY = state.translateY + (cursorY - state.translateY - cursorY / oldScale) * (1 - 1 / oldScale);drawGRSWallWithLoads(state);});}if (fitButton){fitButton.addEventListener('click',function(){state.scale = 1;state.translateX = 0;state.translateY = 0;drawGRSWallWithLoads(state);});}}function setupScreenshotFunctionality(){const screenshotButton = document.getElementById('screenshot-button');const externalLoadsCanvas = document.getElementById('geometry2-canvas');const externalLoadsForm = document.getElementById('externalloadsform');if (screenshotButton && externalLoadsCanvas && externalLoadsForm){const newScreenshotButton = screenshotButton.cloneNode(true);screenshotButton.parentNode.replaceChild(newScreenshotButton,screenshotButton);newScreenshotButton.addEventListener('click',handleExternalLoadsScreenshot);}}function handleExternalLoadsScreenshot(){const externalLoadsForm = document.getElementById('externalloadsform');if (!externalLoadsForm) return;const canvas = document.getElementById('geometry2-canvas');if (canvas){const dataURL = canvas.toDataURL('image/png');storeScreenshotOnServer(dataURL);const link = document.createElement('a');link.href = dataURL;link.download = 'grs_wall_external_loads.png';link.click();}}function storeScreenshotOnServer(dataURL){fetch('/store-screenshot',{method: 'POST',headers:{'Content-Type': 'application/json',},body: JSON.stringify({screenshot: dataURL})})
.then(response => response.json())
.then(data =>{if (data.status === 'success'){console.log('Screenshot stored successfully for report generation:',data.screenshot_id);}else{console.error('Failed to store screenshot:',data.message);}})
.catch(error =>{console.error('Error storing screenshot:',error);});}function performInitialDraw(state){drawGRSWallWithLoads(state);window.externalLoadsRedrawCanvas = function(){drawGRSWallWithLoads(state);};}window.initializeExternalLoadsVisualization = initializeExternalLoadsVisualization;if (document.readyState === 'loading'){document.addEventListener('DOMContentLoaded',initializeExternalLoadsVisualization);}else{initializeExternalLoadsVisualization();}})();if (typeof window !== 'undefined' && window.location && window.location.hostname === 'localhost'){console.log('✅ External Loads Visualization loaded (development mode)');}if (window.location.hostname === 'localhost'){console.log("LayoutVisualization.js script starting to load...");}const domCache = new Map();function getCachedElement(id){if (!domCache.has(id)){domCache.set(id,document.getElementById(id));}return domCache.get(id);}let eventListeners = [];let timeouts = [];function addEventListenerTracked(element,event,handler,options){element.addEventListener(event,handler,options);eventListeners.push({element,event,handler,options});}function cleanup(){eventListeners.forEach(({element,event,handler,options}) =>{if (element && element.removeEventListener){element.removeEventListener(event,handler,options);}});eventListeners = [];timeouts.forEach(id => clearTimeout(id));timeouts = [];domCache.clear();}window.layoutVisualizationCleanup = cleanup;window.initializeLayoutVisualization = function initializeLayoutVisualization(){if (window.location.hostname === 'localhost'){console.log('Initializing Layout Visualization...');}const reinforcementLayoutTable = getCachedElement('reinforcementLayoutTable');if (!reinforcementLayoutTable){if (window.location.hostname === 'localhost'){console.log('Reinforcement layout table not found - skipping layout visualization (probably on different page)');}return;}if (window.location.hostname === 'localhost'){console.log('Reinforcement layout table found - proceeding with visualization initialization');}let geometryData,externalloads_data,reinforcementLayoutData;try{const [geometryRaw,externalLoadsRaw,reinforcementLayoutRaw] = [
localStorage.getItem('geometryData'),localStorage.getItem('externalloads_data'),localStorage.getItem('reinforcementLayoutData')
];geometryData = geometryRaw ? JSON.parse(geometryRaw) :{};externalloads_data = externalLoadsRaw ? JSON.parse(externalLoadsRaw) :{};reinforcementLayoutData = reinforcementLayoutRaw ? JSON.parse(reinforcementLayoutRaw) : [];if (window.location.hostname === 'localhost'){console.log('Loaded geometry data:',geometryData);console.log('Loaded external loads data:',externalloads_data);console.log('Loaded reinforcement layout data:',reinforcementLayoutData);}}catch (error){console.error('Error parsing localStorage data:',error);geometryData ={};externalloads_data ={};reinforcementLayoutData = [];}function getCurrentGeometryData(){let currentData ={wallHeight: 5.0,embedmentDepth: 1.0,wallLength: 6.0,wallBatter: 0.0,backslopeAngle: 0.0,backslopeRise: 2.0};try{const wallHeightField = document.getElementById('wall-height');const embedmentDepthField = document.getElementById('embedment-depth');const wallLengthField = document.getElementById('wall-length');const wallBatterField = document.getElementById('wall-batter');const backslopeAngleField = document.getElementById('backslope-angle');const backslopeRiseField = document.getElementById('backslope-rise');if (wallHeightField){const value = wallHeightField.value.trim();currentData.wallHeight = value ? parseFloat(value) : 5.0;}if (embedmentDepthField){const value = embedmentDepthField.value.trim();currentData.embedmentDepth = value ? parseFloat(value) : 1.0;}if (wallLengthField){const value = wallLengthField.value.trim();currentData.wallLength = value ? parseFloat(value) : 6.0;}if (wallBatterField){const value = wallBatterField.value.trim();currentData.wallBatter = value ? parseFloat(value) : 0.0;}if (backslopeAngleField){const value = backslopeAngleField.value.trim();currentData.backslopeAngle = value ? parseFloat(value) : 0.0;}if (backslopeRiseField){const value = backslopeRiseField.value.trim();currentData.backslopeRise = value ? parseFloat(value) : 2.0;}console.log('🎨 Read current geometry form values:',currentData);}catch (error){console.log('🎨 Could not read geometry form values (probably not on geometry page):',error);}const hasFormData = document.getElementById('wall-height') !== null;if (!hasFormData && geometryData && Object.keys(geometryData).length > 0){console.log('🎨 Using localStorage geometry data as fallback');currentData.wallHeight = geometryData.wallHeight || geometryData['wall-height'] || 5.0;currentData.embedmentDepth = geometryData.embedmentDepth || geometryData['embedment-depth'] || 1.0;currentData.wallLength = geometryData.wallLength || geometryData['wall-length'] || 6.0;currentData.wallBatter = geometryData.wallBatter || geometryData['wall-batter'] || 0.0;currentData.backslopeAngle = geometryData.backslopeAngle || geometryData['backslope-angle'] || 0.0;currentData.backslopeRise = geometryData.backslopeRise || geometryData['backslope-rise'] || 2.0;}else if (!hasFormData){console.log('🎨 No localStorage geometry data found - using template placeholder values for visualization only (NOT setting localStorage)');}console.log('🎨 Final geometry data for visualization:',currentData);return currentData;}const dead_loads = externalloads_data.dead_loads || [0,0,0];const live_loads = externalloads_data.live_loads || [0,0,0];const vertical_strip_load = externalloads_data.vertical_strip_load || [0,0,0];const horizontal_strip_load = externalloads_data.horizontal_strip_load || [0,0,0];const strip_load_width = externalloads_data.strip_load_width || [0,0,0];const strip_load_distance = externalloads_data.strip_load_distance || [0,0,0];const earthquake_acceleration = externalloads_data.earthquake_acceleration || 0;console.log('🎨 Using loads data:',{dead_loads: dead_loads,live_loads: live_loads,deadSum: dead_loads.reduce((a,b) => a + b,0),liveSum: live_loads.reduce((a,b) => a + b,0)});console.log('Layout visualization data loaded successfully');const canvas = document.getElementById('geometry2-canvas');if (!canvas){return;}const ctx = canvas.getContext('2d');if (!ctx){return;}let scale = 1;let translateX = 0;let translateY = 0;let isDown = false;let lastX,lastY;canvas.width = 1000;canvas.height = 600;function drawGRSWall(wallHeight,embedmentDepth,wallLength,wallBatter,backslopeAngle,backslopeRise,dead_loads,live_loads,vertical_strip_load,horizontal_strip_load,strip_load_width,strip_load_distance,earthquake_acceleration,reinforcementLayout){ctx.clearRect(0,0,canvas.width,canvas.height);ctx.save();ctx.translate(translateX,translateY);ctx.scale(scale,scale);const baseScale = 50;const baseX = 100,baseY = 400;const batterOffset = Math.tan((wallBatter * Math.PI) / 180) * (wallHeight * baseScale);const fasciaThickness = 0.2 * baseScale;const fascia ={x1: baseX - fasciaThickness,y1: baseY,x2: baseX - fasciaThickness + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + batterOffset + fasciaThickness,y3: baseY - (wallHeight * baseScale),x4: baseX,y4: baseY};const reinforcedFill ={x1: baseX,y1: baseY,x2: baseX + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + (wallLength * baseScale) + batterOffset,y3: baseY - (wallHeight * baseScale),x4: baseX + (wallLength * baseScale),y4: baseY};const retainedFill ={x1: baseX + (wallLength * baseScale),y1: baseY,x2: baseX + (wallLength * baseScale) + batterOffset,y2: baseY - (wallHeight * baseScale),x3: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y3: baseY - (wallHeight * baseScale),x4: baseX + (wallLength * baseScale) + (2 * wallHeight * baseScale),y4: baseY};const embedment ={x1: baseX - 10 - (1 * wallHeight * baseScale),y1: baseY,x2: baseX - 10 + batterOffset,y2: baseY - (embedmentDepth * baseScale)};const foundationSoil ={x1: embedment.x1,y1: baseY + (0.5*wallHeight * baseScale),x2: retainedFill.x4,y2: baseY};function drawArrow(ctx,x1,y1,x2,y2,arrowSize = 0.05){ctx.strokeStyle = "#000";ctx.fillStyle = "#000";const dx = x2 - x1;const dy = y2 - y1;const angle = Math.atan2(dy,dx);const arrowLength = Math.sqrt(dx * dx + dy * dy) * arrowSize;ctx.save();ctx.translate(x2,y2);ctx.rotate(angle);ctx.beginPath();ctx.moveTo(0,0);ctx.lineTo(-arrowLength,-arrowLength / 2);ctx.lineTo(-arrowLength,arrowLength / 2);ctx.closePath();ctx.fill();ctx.restore();ctx.save();ctx.translate(x1,y1);ctx.rotate(angle + Math.PI);ctx.beginPath();ctx.moveTo(0,0);ctx.lineTo(-arrowLength,-arrowLength / 2);ctx.lineTo(-arrowLength,arrowLength / 2);ctx.closePath();ctx.fill();ctx.restore();ctx.beginPath();ctx.moveTo(x1,y1);ctx.lineTo(x2,y2);ctx.stroke();}const backslopeAngleRadians = (backslopeAngle * Math.PI) / 180;const slopeBaseLength = backslopeAngle > 0 ? backslopeRise / Math.tan(backslopeAngleRadians) : 0;const slopeStartX = reinforcedFill.x2;const slopeStartY = reinforcedFill.y2;let slopeEndX = slopeStartX + (slopeBaseLength * baseScale);let slopeEndY = slopeStartY - (backslopeRise * baseScale);ctx.fillStyle = "#FFA500";if (backslopeAngle > 0){if (slopeEndX <= retainedFill.x3){const horizontalEndX = retainedFill.x3;const horizontalEndY = slopeEndY;ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeEndX,slopeEndY);ctx.lineTo(horizontalEndX,horizontalEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.lineTo(horizontalEndX,horizontalEndY);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.closePath();ctx.fill();const topRise = backslopeRise;ctx.beginPath();ctx.moveTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x3,retainedFill.y3 - topRise * baseScale);ctx.stroke();drawArrow(ctx,retainedFill.x3,retainedFill.y3,retainedFill.x3,retainedFill.y3 - topRise * baseScale,0.1);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.fillText(`${topRise.toFixed(2)}m`,retainedFill.x3 - 40,retainedFill.y3 - (topRise * baseScale) / 2);}else{slopeEndX = retainedFill.x3;slopeEndY = slopeStartY - ((slopeEndX - slopeStartX) * Math.tan(backslopeAngleRadians));ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.stroke();ctx.beginPath();ctx.moveTo(slopeStartX,slopeStartY);ctx.lineTo(slopeEndX,slopeEndY);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.closePath();ctx.fill();const topRise = (retainedFill.x3 - reinforcedFill.x2) * Math.tan(backslopeAngleRadians) / baseScale;ctx.beginPath();ctx.moveTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x3,retainedFill.y3 - topRise * baseScale);ctx.stroke();drawArrow(ctx,retainedFill.x3,retainedFill.y3,retainedFill.x3,retainedFill.y3 - (topRise * baseScale),0.1);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.fillText(`${topRise.toFixed(2)}m`,retainedFill.x3 - 40,retainedFill.y3 - (topRise * 0.5 * baseScale));}}else{const topRise = 0.0;ctx.strokeStyle = "#000";ctx.lineWidth = 1;ctx.beginPath();ctx.moveTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x3,retainedFill.y3 - topRise * baseScale);ctx.stroke();drawArrow(ctx,retainedFill.x3,retainedFill.y3,retainedFill.x3,retainedFill.y3 - topRise * baseScale,0.1);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.fillText(`${topRise.toFixed(2)}m`,retainedFill.x3 - 40,retainedFill.y3 - (topRise * baseScale) / 2);}ctx.fillStyle = "#A98B6D";ctx.fillRect(foundationSoil.x1,foundationSoil.y1,foundationSoil.x2 - foundationSoil.x1,foundationSoil.y2 - foundationSoil.y1);ctx.fillRect(embedment.x1,embedment.y1,embedment.x2 - embedment.x1,embedment.y2 - embedment.y1);ctx.fillStyle = "#666";ctx.beginPath();ctx.moveTo(fascia.x1,fascia.y1);ctx.lineTo(fascia.x2,fascia.y2);ctx.lineTo(fascia.x3,fascia.y3);ctx.lineTo(fascia.x4,fascia.y4);ctx.fill();ctx.fillStyle = "#D6B85A";ctx.beginPath();ctx.moveTo(reinforcedFill.x1,reinforcedFill.y1);ctx.lineTo(reinforcedFill.x2,reinforcedFill.y2);ctx.lineTo(reinforcedFill.x3,reinforcedFill.y3);ctx.lineTo(reinforcedFill.x4,reinforcedFill.y4);ctx.fill();ctx.fillStyle = "#D2B48C";ctx.beginPath();ctx.moveTo(retainedFill.x1,retainedFill.y1);ctx.lineTo(retainedFill.x2,retainedFill.y2);ctx.lineTo(retainedFill.x3,retainedFill.y3);ctx.lineTo(retainedFill.x4,retainedFill.y4);ctx.fill();ctx.strokeStyle = "#000";ctx.lineWidth = 1;ctx.beginPath();ctx.moveTo(baseX - 20,baseY);ctx.lineTo(baseX - 20,baseY - (wallHeight * baseScale));ctx.stroke();drawArrow(ctx,baseX - 20,baseY,baseX - 20,baseY - (wallHeight * baseScale),0.05);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${wallHeight}m`,baseX - 40,baseY - (0.5 * wallHeight * baseScale) - 10);ctx.beginPath();ctx.moveTo(baseX,baseY);ctx.lineTo(baseX + (wallLength * baseScale),baseY);ctx.stroke();drawArrow(ctx,baseX,baseY,baseX + (wallLength * baseScale),baseY,0.05);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(`${wallLength}m`,baseX + ((wallLength * baseScale) / 2),baseY + 10);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(`Reinforced Fill`,baseX + ((0.5 * wallLength * baseScale)),baseY - (0.5 * wallHeight * baseScale) - 10);ctx.fillText(`Retained Fill`,baseX + ((1.5 * wallLength * baseScale)),baseY - (0.5 * wallHeight * baseScale) - 10);ctx.fillText(`Foundation Soil`,baseX + ((1.0 * wallLength * baseScale)),baseY + (0.25 * wallHeight * baseScale) - 10);ctx.beginPath();ctx.moveTo(baseX - 40,baseY);ctx.lineTo(baseX - 40,baseY - (embedmentDepth * baseScale));ctx.stroke();drawArrow(ctx,baseX - 40,baseY,baseX - 40,baseY - (embedmentDepth * baseScale),0.2);ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${embedmentDepth}m`,baseX - 50 - fasciaThickness,baseY - (0.5 * embedmentDepth * baseScale) - 10);function drawArrow1(ctx,fromX,fromY,toX,toY,size,color,hollow = false){const angle = Math.atan2(toY - fromY,toX - fromX);ctx.strokeStyle = color;ctx.lineWidth = 2;ctx.beginPath();ctx.moveTo(fromX,fromY);ctx.lineTo(toX,toY);if (hollow){ctx.stroke();}else{ctx.lineTo(toX - size * Math.cos(angle - Math.PI / 6),toY - size * Math.sin(angle - Math.PI / 6));ctx.moveTo(toX,toY);ctx.lineTo(toX - size * Math.cos(angle + Math.PI / 6),toY - size * Math.sin(angle + Math.PI / 6));ctx.stroke();}}function drawLoads(ctx,startX,endX,y,loadValue,color,label){ctx.strokeStyle = color;ctx.lineWidth = 2;ctx.font = "18px Arial";ctx.fillStyle = color;ctx.textAlign = "center";ctx.textBaseline = "top";const arrowSpacing = 50;for (let x = startX;x <= endX;x += arrowSpacing){drawArrow1(ctx,x,y,x,y + 20,5,color);}ctx.fillText(`${label}: ${loadValue.toFixed(2)}kpa`,(startX + endX) / 2,y - 25);}function drawStripLoads(ctx,startX,y,verticalStripLoads,horizontalStripLoads,stripLoadWidths,stripLoadDistances){const stripLoadColor = "purple";for (let i = 0;i < verticalStripLoads.length;i++){if (verticalStripLoads[i] > 0 || horizontalStripLoads[i] > 0){const stripLoadX = startX + (stripLoadDistances[i] * baseScale) - (stripLoadWidths[i] * baseScale / 2);const centerX = stripLoadX + (stripLoadWidths[i] * baseScale / 2);if (stripLoadX <= retainedFill.x3){ctx.fillStyle = stripLoadColor;ctx.fillRect(stripLoadX,y,stripLoadWidths[i] * baseScale,20);if (verticalStripLoads[i] > 0){drawArrow1(ctx,centerX,y - 40,centerX,y,5,stripLoadColor);ctx.font = "14px Arial";ctx.fillStyle = stripLoadColor;ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`${verticalStripLoads[i].toFixed(2)}kn/m`,centerX,y - 30);}if (horizontalStripLoads[i] > 0){drawArrow1(ctx,centerX,y - 5,centerX - 30,y - 5,5,stripLoadColor);ctx.font = "14px Arial";ctx.fillStyle = stripLoadColor;ctx.textAlign = "right";ctx.textBaseline = "middle";ctx.fillText(`${horizontalStripLoads[i].toFixed(2)}kn/m`,centerX - 35,y);}}else{ctx.font = "14px Arial";ctx.fillStyle = stripLoadColor;ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText(`Strip Load ${i + 1}: Beyond Influence Zone`,retainedFill.x3 + 50,y - 40);}}}}function drawEarthquakeAcceleration(ctx,x,y,acceleration){const arrowLength = 50;const arrowSize = 10;const label = `${acceleration.toFixed(2)}g`;ctx.strokeStyle = "#000";ctx.lineWidth = 2;ctx.beginPath();ctx.moveTo(x,y);ctx.lineTo(x - arrowLength,y);ctx.lineTo(x - arrowLength + arrowSize,y - arrowSize / 2);ctx.moveTo(x - arrowLength,y);ctx.lineTo(x - arrowLength + arrowSize,y + arrowSize / 2);ctx.stroke();ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "middle";ctx.fillText(label,x - arrowLength - 30,y);}const deadLoadSum = dead_loads.reduce((a,b) => a + b,0);const liveLoadSum = live_loads.reduce((a,b) => a + b,0);if (backslopeAngle === 0){const loadY = reinforcedFill.y2 - 50;drawLoads(ctx,reinforcedFill.x2,retainedFill.x3,loadY,deadLoadSum,"red","Dead Load");drawLoads(ctx,reinforcedFill.x2,retainedFill.x3,loadY - 50,liveLoadSum,"blue","Live Load");}else if (slopeEndX < retainedFill.x3){const loadY = slopeEndY - 50;drawLoads(ctx,slopeEndX,retainedFill.x3,loadY,deadLoadSum,"red","Dead Load");drawLoads(ctx,slopeEndX,retainedFill.x3,loadY - 50,liveLoadSum,"blue","Live Load");}else{ctx.font = "18px Arial";ctx.fillStyle = "#000";ctx.textAlign = "center";ctx.textBaseline = "top";ctx.fillText("Dead and Live Loads: Beyond Influence Zone",(reinforcedFill.x2 + retainedFill.x3) / 2,reinforcedFill.y2 - 50);}const stripLoadY = (backslopeAngle === 0 ? reinforcedFill.y2 : slopeEndY) - 20;drawStripLoads(ctx,reinforcedFill.x2,stripLoadY,vertical_strip_load,horizontal_strip_load,strip_load_width,strip_load_distance);drawEarthquakeAcceleration(ctx,(retainedFill.x1 + retainedFill.x3) / 2,(retainedFill.y1 + retainedFill.y3 + 60) / 2,earthquake_acceleration);const gradeColors ={};const getRandomColor = () => `rgb(${Math.floor(Math.random() * 255)},${Math.floor(Math.random() * 255)},${Math.floor(Math.random() * 255)})`;if (reinforcementLayout && reinforcementLayout.length > 0){reinforcementLayout.forEach(data =>{const{location,length,reinforcement_type}= data;if (!location || !length || !reinforcement_type) return;const locationFromBottom = location * baseScale;const lengthOfReinforcement = length * baseScale;if (!gradeColors[reinforcement_type]){gradeColors[reinforcement_type] = getRandomColor();}const color = gradeColors[reinforcement_type];const yPos = reinforcedFill.y1 - locationFromBottom;const startX = reinforcedFill.x1 + Math.tan((wallBatter * Math.PI) / 180) * locationFromBottom;const endX = reinforcedFill.x1 + Math.tan((wallBatter * Math.PI) / 180) * locationFromBottom + lengthOfReinforcement;ctx.strokeStyle = color;ctx.lineWidth = 2;ctx.beginPath();ctx.moveTo(startX,yPos);ctx.lineTo(endX,yPos);ctx.stroke();ctx.fillStyle = color;ctx.font = "12px Arial";ctx.fillText(`L = ${(lengthOfReinforcement / baseScale).toFixed(2)}m,${reinforcement_type}`,startX + lengthOfReinforcement / 2 - 30,yPos - 10);});}ctx.restore();}function redrawCanvas(){const currentGeometry = getCurrentGeometryData();drawGRSWall(
currentGeometry.wallHeight,currentGeometry.embedmentDepth,currentGeometry.wallLength,currentGeometry.wallBatter,currentGeometry.backslopeAngle,currentGeometry.backslopeRise,dead_loads,live_loads,vertical_strip_load,horizontal_strip_load,strip_load_width,strip_load_distance,earthquake_acceleration,reinforcementLayoutData
);}const zoomInButton = document.getElementById('zoom-in-button');const zoomOutButton = document.getElementById('zoom-out-button');if (zoomInButton && zoomOutButton){zoomInButton.addEventListener('click',function(){const cursorX = canvas.width / 2;const cursorY = canvas.height / 2;const oldScale = scale;scale *= 1.1;translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);redrawCanvas();});zoomOutButton.addEventListener('click',function(){const cursorX = canvas.width / 2;const cursorY = canvas.height / 2;const oldScale = scale;scale *= 0.9;translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);redrawCanvas();});}const fitButton = document.getElementById('fit-button');if (fitButton){fitButton.addEventListener('click',function(){scale = 1;translateX = 0;translateY = 0;redrawCanvas();});}canvas.addEventListener('wheel',function(e){e.preventDefault();const rect = canvas.getBoundingClientRect();const cursorX = e.clientX - rect.left;const cursorY = e.clientY - rect.top;const oldScale = scale;scale *= e.deltaY > 0 ? 0.9 : 1.1;translateX = translateX + (cursorX - translateX - cursorX / oldScale) * (1 - 1 / oldScale);translateY = translateY + (cursorY - translateY - cursorY / oldScale) * (1 - 1 / oldScale);redrawCanvas();});canvas.addEventListener('mousedown',function(e){isDown = true;canvas.style.cursor = 'grabbing';const rect = canvas.getBoundingClientRect();lastX = e.clientX - rect.left;lastY = e.clientY - rect.top;});canvas.addEventListener('mouseup',function(){isDown = false;canvas.style.cursor = 'grab';});canvas.addEventListener('mousemove',function(e){if (isDown){const rect = canvas.getBoundingClientRect();const currentX = e.clientX - rect.left;const currentY = e.clientY - rect.top;translateX += currentX - lastX;translateY += currentY - lastY;lastX = currentX;lastY = currentY;redrawCanvas();}});window.redrawCanvas = function(updatedReinforcementLayout){console.log("🎨 redrawCanvas called with updated layout:",updatedReinforcementLayout);let freshGeometryData,freshExternalLoadsData,freshLayoutData;try{freshGeometryData = JSON.parse(localStorage.getItem('geometryData')) ||{};freshExternalLoadsData = JSON.parse(localStorage.getItem('externalloads_data')) ||{};freshLayoutData = updatedReinforcementLayout || JSON.parse(localStorage.getItem('reinforcementLayoutData')) || [];console.log("🎨 Fresh data loaded:",{geometry: freshGeometryData,externalLoads: freshExternalLoadsData,layout: freshLayoutData});}catch (error){console.error('Error loading fresh data:',error);freshGeometryData = geometryData;freshExternalLoadsData = externalloads_data;freshLayoutData = updatedReinforcementLayout || reinforcementLayoutData;}geometryData = freshGeometryData;externalloads_data = freshExternalLoadsData;if (updatedReinforcementLayout){reinforcementLayoutData = updatedReinforcementLayout;}else{reinforcementLayoutData = freshLayoutData;}const freshWallHeight = geometryData.wallHeight || geometryData['wall-height'] || 5;const freshEmbedmentDepth = geometryData.embedmentDepth || geometryData['embedment-depth'] || 1;const freshWallLength = geometryData.wallLength || geometryData['wall-length'] || 6;const freshWallBatter = geometryData.wallBatter || geometryData['wall-batter'] || 0;const freshBackslopeAngle = geometryData.backslopeAngle || geometryData['backslope-angle'] || 0;const freshBackslopeRise = geometryData.backslopeRise || geometryData['backslope-rise'] || 2;const freshDeadLoads = externalloads_data.dead_loads || [0,0,0];const freshLiveLoads = externalloads_data.live_loads || [0,0,0];const freshVerticalStripLoad = externalloads_data.vertical_strip_load || [0,0,0];const freshHorizontalStripLoad = externalloads_data.horizontal_strip_load || [0,0,0];const freshStripLoadWidth = externalloads_data.strip_load_width || [0,0,0];const freshStripLoadDistance = externalloads_data.strip_load_distance || [0,0,0];const freshEarthquakeAcceleration = externalloads_data.earthquake_acceleration || 0;console.log("🎨 Drawing with fresh values:",{wallHeight: freshWallHeight,embedmentDepth: freshEmbedmentDepth,wallLength: freshWallLength,layoutData: reinforcementLayoutData});drawGRSWall(
freshWallHeight,freshEmbedmentDepth,freshWallLength,freshWallBatter,freshBackslopeAngle,freshBackslopeRise,freshDeadLoads,freshLiveLoads,freshVerticalStripLoad,freshHorizontalStripLoad,freshStripLoadWidth,freshStripLoadDistance,freshEarthquakeAcceleration,reinforcementLayoutData
);};function updateReinforcementLayoutFromForm(){const table = document.getElementById('reinforcementLayoutTable');if (!table) return;const updatedLayout = [];const rows = table.querySelectorAll('tbody tr');if (rows) {rows.forEach((row) =>{const locationInput = row.querySelector('input[name="location"]');const lengthInput = row.querySelector('input[name="length"]');const typeSelect = row.querySelector('select[name="reinforcement_type"]');if (locationInput && lengthInput && typeSelect){const location = parseFloat(locationInput.value);const length = parseFloat(lengthInput.value);const type = typeSelect.value;if (!isNaN(location) && !isNaN(length) && type){updatedLayout.push({location: location,length: length,reinforcement_type: type});}}});}reinforcementLayoutData = updatedLayout;console.log("Updated reinforcement layout data:",reinforcementLayoutData);window.redrawCanvas(reinforcementLayoutData);}window.updateReinforcementLayoutFromForm = updateReinforcementLayoutFromForm;redrawCanvas();const screenshotButton = document.getElementById('screenshot-button');const layoutTable = document.getElementById('reinforcementLayoutTable');if (screenshotButton && layoutTable){const newScreenshotButton = screenshotButton.cloneNode(true);screenshotButton.parentNode.replaceChild(newScreenshotButton,screenshotButton);newScreenshotButton.addEventListener('click',handleLayoutScreenshot);console.log("Attached layout screenshot button listener (cleaned)");}else{console.log("Screenshot button setup failed:",{screenshotButton: !!screenshotButton,layoutTable: !!layoutTable});}function handleLayoutScreenshot(){console.log("Layout screenshot button clicked");const layoutTable = document.getElementById('reinforcementLayoutTable');if (!layoutTable){console.log("Layout table not found - not on reinforcement layout page");return;}const canvas = document.getElementById('geometry2-canvas');if (!canvas){return;}console.log("Taking screenshot of reinforcement layout canvas");const dataURL = canvas.toDataURL('image/png');storeScreenshotOnServer(dataURL);const link = document.createElement('a');link.href = dataURL;link.download = 'grs_wall_reinforcement_layout.png';link.click();console.log("Reinforcement layout screenshot downloaded");}function storeScreenshotOnServer(dataURL){fetch('/store-screenshot',{method: 'POST',headers:{'Content-Type': 'application/json',},body: JSON.stringify({screenshot: dataURL})})
.then(response => response.json())
.then(data =>{if (data.status === 'success'){console.log('Screenshot stored successfully for report generation:',data.screenshot_id);}else{console.error('Failed to store screenshot:',data.message);}})
.catch(error =>{console.error('Error storing screenshot:',error);});}function captureLayoutScreenshotForReport(){const canvas = document.getElementById('geometry2-canvas');if (canvas){console.log("Auto-capturing layout screenshot for report");const dataURL = canvas.toDataURL('image/png');storeScreenshotOnServer(dataURL);}}window.captureLayoutScreenshotForReport = captureLayoutScreenshotForReport;function autoCapture(){setTimeout(() =>{const canvas = document.getElementById('geometry2-canvas');if (canvas){console.log("Auto-capturing screenshot for report generation");captureLayoutScreenshotForReport();}},1000);}const originalRedrawCanvas = window.redrawCanvas;if (originalRedrawCanvas){window.redrawCanvas = function(){originalRedrawCanvas.apply(this,arguments);autoCapture();};}};document.addEventListener('DOMContentLoaded',function(){console.log("🎨 DOM ready - calling initializeLayoutVisualization");window.initializeLayoutVisualization();});console.log("🎨 LayoutVisualization.js loaded - function available globally");