/**
 * CSS Performance Monitor
 * Tracks CSS loading performance and provides optimization insights
 * @version 1.0.0 - Performance Optimized
 */

(function() {
    'use strict';

    // Only run performance monitoring in development
    if (window.location.hostname !== 'localhost') {
        return;
    }

    const CSSPerformanceMonitor = {
        startTime: performance.now(),
        cssLoadTimes: new Map(),
        
        init: function() {
            this.monitorCSSLoading();
            this.measureRenderBlocking();
            this.trackFirstContentfulPaint();
            this.reportPerformanceMetrics();
        },

        monitorCSSLoading: function() {
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"], link[rel="preload"][as="style"]');
            
            stylesheets.forEach((link, index) => {
                const href = link.href;
                const startTime = performance.now();
                
                // Monitor load event
                link.addEventListener('load', () => {
                    const loadTime = performance.now() - startTime;
                    this.cssLoadTimes.set(href, {
                        loadTime: loadTime,
                        type: link.rel === 'preload' ? 'async' : 'blocking',
                        index: index
                    });
                    
                    console.log(`📊 CSS Loaded [${index}]: ${href.split('/').pop()}`);
                    console.log(`   Load time: ${loadTime.toFixed(2)}ms`);
                    console.log(`   Type: ${link.rel === 'preload' ? 'Non-blocking (async)' : 'Render-blocking'}`);
                });

                // Monitor error event
                link.addEventListener('error', () => {
                    console.error(`❌ CSS Load Error: ${href}`);
                });
            });
        },

        measureRenderBlocking: function() {
            // Measure time to first render
            const observer = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach((entry) => {
                    if (entry.entryType === 'paint') {
                        console.log(`🎨 ${entry.name}: ${entry.startTime.toFixed(2)}ms`);
                        
                        if (entry.name === 'first-contentful-paint') {
                            this.analyzeCSSImpact(entry.startTime);
                        }
                    }
                });
            });

            try {
                observer.observe({ entryTypes: ['paint'] });
            } catch (e) {
                console.warn('Paint timing not supported in this browser');
            }
        },

        trackFirstContentfulPaint: function() {
            // Track when CSS stops blocking rendering
            if ('requestIdleCallback' in window) {
                requestIdleCallback(() => {
                    this.reportCSSOptimizations();
                });
            } else {
                setTimeout(() => {
                    this.reportCSSOptimizations();
                }, 100);
            }
        },

        analyzeCSSImpact: function(fcpTime) {
            const totalCSSFiles = document.querySelectorAll('link[rel="stylesheet"]').length;
            const asyncCSSFiles = document.querySelectorAll('link[rel="preload"][as="style"]').length;
            
            console.log(`📈 CSS Performance Analysis:`);
            console.log(`   First Contentful Paint: ${fcpTime.toFixed(2)}ms`);
            console.log(`   Render-blocking CSS files: ${totalCSSFiles}`);
            console.log(`   Async CSS files: ${asyncCSSFiles}`);
            
            // Performance recommendations
            if (totalCSSFiles > 2) {
                console.warn(`⚠️  Consider consolidating CSS files (currently ${totalCSSFiles} files)`);
            }
            
            if (asyncCSSFiles === 0) {
                console.warn(`⚠️  Consider loading non-critical CSS asynchronously`);
            }
            
            if (fcpTime > 1500) {
                console.warn(`⚠️  First Contentful Paint is slow (${fcpTime.toFixed(2)}ms > 1500ms)`);
                console.log(`💡 Optimization suggestions:`);
                console.log(`   - Inline critical CSS`);
                console.log(`   - Use CSS preload for non-critical styles`);
                console.log(`   - Minimize CSS file sizes`);
            } else {
                console.log(`✅ Good First Contentful Paint performance!`);
            }
        },

        reportCSSOptimizations: function() {
            const totalFiles = this.cssLoadTimes.size;
            let totalLoadTime = 0;
            let blockingFiles = 0;
            let asyncFiles = 0;

            this.cssLoadTimes.forEach((data, href) => {
                totalLoadTime += data.loadTime;
                if (data.type === 'blocking') {
                    blockingFiles++;
                } else {
                    asyncFiles++;
                }
            });

            console.log(`\n🚀 CSS Optimization Report:`);
            console.log(`   Total CSS files: ${totalFiles}`);
            console.log(`   Render-blocking: ${blockingFiles}`);
            console.log(`   Async loaded: ${asyncFiles}`);
            console.log(`   Average load time: ${totalFiles > 0 ? (totalLoadTime / totalFiles).toFixed(2) : 0}ms`);
            
            // Calculate optimization score
            let score = 100;
            if (blockingFiles > 2) score -= (blockingFiles - 2) * 10;
            if (asyncFiles === 0 && totalFiles > 1) score -= 20;
            if (totalLoadTime / totalFiles > 100) score -= 15;
            
            console.log(`   Optimization Score: ${Math.max(0, score)}/100`);
            
            if (score >= 80) {
                console.log(`   ✅ Excellent CSS performance!`);
            } else if (score >= 60) {
                console.log(`   ⚠️  Good CSS performance, room for improvement`);
            } else {
                console.log(`   ❌ CSS performance needs optimization`);
            }
        },

        reportPerformanceMetrics: function() {
            // Report after page load
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const loadTime = performance.now() - this.startTime;
                    console.log(`\n⏱️  Total Page Load Time: ${loadTime.toFixed(2)}ms`);
                    
                    // Memory usage if available
                    if (performance.memory) {
                        const memory = performance.memory;
                        console.log(`🧠 Memory Usage:`);
                        console.log(`   Used: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
                        console.log(`   Total: ${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`);
                    }
                    
                    // Resource timing for CSS files
                    if (performance.getEntriesByType) {
                        const resources = performance.getEntriesByType('resource');
                        const cssResources = resources.filter(r => r.name.includes('.css'));
                        
                        if (cssResources.length > 0) {
                            console.log(`\n📊 CSS Resource Timing:`);
                            cssResources.forEach(resource => {
                                const filename = resource.name.split('/').pop();
                                console.log(`   ${filename}:`);
                                console.log(`     DNS: ${(resource.domainLookupEnd - resource.domainLookupStart).toFixed(2)}ms`);
                                console.log(`     Connect: ${(resource.connectEnd - resource.connectStart).toFixed(2)}ms`);
                                console.log(`     Download: ${(resource.responseEnd - resource.responseStart).toFixed(2)}ms`);
                                console.log(`     Total: ${resource.duration.toFixed(2)}ms`);
                            });
                        }
                    }
                }, 1000);
            });
        },

        // Public method to get performance data
        getPerformanceData: function() {
            return {
                cssLoadTimes: Array.from(this.cssLoadTimes.entries()),
                totalFiles: this.cssLoadTimes.size,
                pageLoadTime: performance.now() - this.startTime
            };
        }
    };

    // Initialize monitoring when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            CSSPerformanceMonitor.init();
        });
    } else {
        CSSPerformanceMonitor.init();
    }

    // Make available globally for debugging
    window.CSSPerformanceMonitor = CSSPerformanceMonitor;

    console.log('🔍 CSS Performance Monitor initialized');

})();
