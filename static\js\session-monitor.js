/**
 * Session Monitoring Script
 * Monitors session validity and automatically logs out invalid sessions
 */

class SessionMonitor {
    constructor() {
        this.checkInterval = 10000; // Check every 10 seconds
        this.intervalId = null;
        this.isChecking = false;
        this.maxRetries = 3;
        this.retryCount = 0;
        this.lastValidCheck = Date.now();
        this.sessionInvalid = false;
        this.logoutInProgress = false; // Prevent multiple logout attempts
        
        // Activity tracking
        this.lastActivityTime = Date.now();
        this.activityTimeout = 1800000; // 30 minutes in milliseconds
        this.activityIntervalId = null;
        
        // Only start monitoring if user is logged in
        if (this.isLoggedIn()) {
            this.startMonitoring();
            this.setupFormInterception();
            this.setupActivityTracking();
        }
    }
    
    isLoggedIn() {
        // Check if we're on a page that requires login (not login page or request access page)
        const currentPath = window.location.pathname;
        const publicPaths = ['/login', '/request_access', '/logout', '/clear_temp_session'];
        return !publicPaths.includes(currentPath);
    }
    
    startMonitoring() {
        console.log('Starting session monitoring...');
        this.intervalId = setInterval(() => {
            this.checkSessionStatus();
        }, this.checkInterval);
        
        // Also check immediately
        this.checkSessionStatus();
        
        // Listen for focus events to check session when user returns to tab
        window.addEventListener('focus', () => {
            if (Date.now() - this.lastValidCheck > 5000) { // Only if last check was more than 5 seconds ago
                this.checkSessionStatus();
            }
        });
        
        // Listen for visibility change events
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && Date.now() - this.lastValidCheck > 5000) {
                this.checkSessionStatus();
            }
        });
    }
    
    setupActivityTracking() {
        console.log('Setting up activity tracking...');
        
        // List of events that count as user activity
        const activityEvents = [
            'click', 'keypress', 'keydown', 'keyup', 'mousemove', 
            'mousedown', 'mouseup', 'scroll', 'touchstart', 'touchmove', 
            'touchend', 'wheel', 'input', 'change', 'focus', 'blur'
        ];
        
        // Add event listeners for all activity events
        activityEvents.forEach(event => {
            document.addEventListener(event, () => {
                this.updateLastActivity();
            }, true);
        });
        
        // Start the activity timeout checker
        this.startActivityTimeoutChecker();
    }
    
    updateLastActivity() {
        this.lastActivityTime = Date.now();
        console.log('User activity detected, activity timer reset');
    }
    
    startActivityTimeoutChecker() {
        // Check for inactivity every 5 seconds
        this.activityIntervalId = setInterval(() => {
            this.checkActivityTimeout();
        }, 5000);
    }
    
    checkActivityTimeout() {
        if (this.sessionInvalid) {
            return; // Already handling invalid session
        }
        
        const timeSinceLastActivity = Date.now() - this.lastActivityTime;
        
        if (timeSinceLastActivity >= this.activityTimeout) {
            console.warn(`No user activity for ${timeSinceLastActivity}ms, logging out due to inactivity`);
            this.sessionInvalid = true;
            this.handleInvalidSession('Session expired due to inactivity');
        } else {
            const remainingTime = this.activityTimeout - timeSinceLastActivity;
            console.log(`User active, ${Math.round(remainingTime / 1000)} seconds until timeout`);
        }
    }
    
    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
            console.log('Session monitoring stopped');
        }
        
        if (this.activityIntervalId) {
            clearInterval(this.activityIntervalId);
            this.activityIntervalId = null;
            console.log('Activity monitoring stopped');
        }
    }
    
    setupFormInterception() {
        // Intercept all form submissions and AJAX calls to check session first
        const originalSubmit = HTMLFormElement.prototype.submit;
        const originalFetch = window.fetch;
        const self = this;
        
        // Intercept form submissions
        HTMLFormElement.prototype.submit = function() {
            if (self.sessionInvalid) {
                self.showSessionExpiredMessage('Cannot submit form - session expired');
                return false;
            }
            return originalSubmit.apply(this, arguments);
        };
        
        // Intercept fetch requests (AJAX)
        window.fetch = function(...args) {
            if (self.sessionInvalid) {
                self.showSessionExpiredMessage('Cannot make request - session expired');
                return Promise.reject(new Error('Session expired'));
            }
            return originalFetch.apply(this, args);
        };
        
        // Intercept click events on buttons and links
        document.addEventListener('click', function(event) {
            if (self.sessionInvalid) {
                const target = event.target;
                if (target.tagName === 'BUTTON' || 
                    target.tagName === 'A' || 
                    target.type === 'submit' ||
                    target.closest('button') ||
                    target.closest('a')) {
                    event.preventDefault();
                    event.stopPropagation();
                    self.showSessionExpiredMessage('Cannot perform action - session expired');
                    return false;
                }
            }
        }, true);
    }
    
    async checkSessionStatus() {
        if (this.isChecking) {
            return; // Prevent concurrent checks
        }
        
        this.isChecking = true;
        
        try {
            const response = await fetch('/check_session_status', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            
            if (data.valid) {
                // Session is valid, reset retry count
                this.retryCount = 0;
                this.lastValidCheck = Date.now();
                this.sessionInvalid = false;
                console.log('Session status: Valid');
            } else {
                // Session is invalid
                console.warn('Session invalid:', data.message);
                this.sessionInvalid = true;
                this.handleInvalidSession(data.message);
            }
            
        } catch (error) {
            console.error('Error checking session status:', error);
            this.retryCount++;
            
            if (this.retryCount >= this.maxRetries) {
                console.error('Max retries reached, assuming session is invalid');
                this.sessionInvalid = true;
                this.handleInvalidSession('Network error - assuming session expired');
            }
        } finally {
            this.isChecking = false;
        }
    }
    
    handleInvalidSession(reason) {
        // Prevent multiple calls to this function
        if (this.logoutInProgress) {
            console.log('Logout already in progress, ignoring duplicate session invalidation');
            return;
        }
        
        console.warn('Handling invalid session:', reason);
        this.logoutInProgress = true;
        
        // Stop monitoring to prevent further checks
        this.stopMonitoring();
        
        // Disable all interactive elements
        this.disablePageInteractions();
        
        // Show user-friendly message
        this.showSessionExpiredMessage(reason);
        
        // For inactivity timeout, logout immediately
        // For other reasons, wait a bit to show the message
        const timeoutDelay = reason.includes('inactivity') ? 2000 : 3000;
        
        setTimeout(() => {
            this.performLogout();
        }, timeoutDelay);
    }
    
    disablePageInteractions() {
        // Add a visual overlay to indicate the page is disabled
        const overlay = document.createElement('div');
        overlay.id = 'session-expired-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 9998;
            pointer-events: auto;
        `;
        
        // Disable all forms and buttons
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.style.pointerEvents = 'none';
            form.style.opacity = '0.5';
        });
        
        const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
        buttons.forEach(button => {
            button.disabled = true;
            button.style.opacity = '0.5';
        });
        
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            link.style.pointerEvents = 'none';
            link.style.opacity = '0.5';
        });
        
        document.body.appendChild(overlay);
    }
    
    showSessionExpiredMessage(reason) {
        // Remove any existing session expired alerts
        const existingAlert = document.getElementById('session-expired-alert');
        if (existingAlert) {
            console.log('Session expired alert already exists, not creating duplicate');
            return;
        }
        
        // Check for global flag to prevent multiple popups across tabs
        if (localStorage.getItem('session_expired_popup_shown')) {
            console.log('Session expired popup already shown in another tab');
            // Still perform logout, just don't show another popup
            setTimeout(() => {
                this.performLogout();
            }, 1000);
            return;
        }
        
        // Set global flag to prevent multiple popups
        try {
            localStorage.setItem('session_expired_popup_shown', 'true');
        } catch (e) {
            console.warn('Could not set localStorage flag:', e);
        }
        
        // Determine the message based on reason
        let title = 'Session Expired';
        let message = 'Your session has expired.';
        let icon = 'fas fa-exclamation-triangle';
        let iconColor = 'text-warning';
        
        if (reason.includes('inactivity')) {
            title = 'Session Timeout';
            message = 'Your session has expired due to 30 minutes of inactivity.';
            icon = 'fas fa-clock';
            iconColor = 'text-info';
        } else if (reason.includes('multiple logins')) {
            title = 'Multiple Sessions Detected';
            message = 'Your session was terminated due to login from another device.';
            icon = 'fas fa-users';
            iconColor = 'text-warning';
        }
        
        // Create and show a centered modal-style alert
        const alertDiv = document.createElement('div');
        alertDiv.id = 'session-expired-alert';
        alertDiv.className = 'position-fixed d-flex align-items-center justify-content-center';
        alertDiv.style.cssText = `
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            backdrop-filter: blur(3px);
        `;
        
        alertDiv.innerHTML = `
            <div class="alert alert-danger m-0 text-center" style="
                min-width: 400px;
                max-width: 500px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                border: none;
                border-radius: 12px;
                background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            ">
                <div class="d-flex flex-column align-items-center">
                    <i class="${icon} fs-1 ${iconColor} mb-3" style="filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));"></i>
                    <h4 class="mb-2 text-danger fw-bold">${title}</h4>
                    <p class="mb-2 text-dark">${message}</p>
                    <small class="text-muted mb-3">You will be redirected to login in a few seconds...</small>
                    <button type="button" class="btn btn-danger btn-lg px-4" onclick="window.sessionMonitor.performLogout()">
                        <i class="fas fa-sign-in-alt me-2"></i>Go to Login Now
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(alertDiv);
    }
    
    performLogout() {
        // Prevent multiple logout attempts
        if (this.logoutInProgress === 'redirecting') {
            console.log('Logout redirect already in progress');
            return;
        }
        
        this.logoutInProgress = 'redirecting';
        
        // Clear local data
        this.clearBrowserData();
        
        // Show centered loading message
        const alert = document.getElementById('session-expired-alert');
        if (alert) {
            alert.innerHTML = `
                <div class="alert alert-info m-0 text-center" style="
                    min-width: 300px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                    border: none;
                    border-radius: 12px;
                    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
                ">
                    <div class="d-flex flex-column align-items-center">
                        <i class="fas fa-spinner fa-spin fs-1 text-info mb-3"></i>
                        <h5 class="mb-2 text-info">Redirecting to login...</h5>
                        <small class="text-muted">Please wait...</small>
                    </div>
                </div>
            `;
        }
        
        // Redirect to login
        setTimeout(() => {
            window.location.href = '/login';
        }, 1000);
    }
    
    clearBrowserData() {
        try {
            // Clear the session expired popup flag first, before clearing localStorage
            localStorage.removeItem('session_expired_popup_shown');
            
            // Clear cookies
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            
            // Clear localStorage
            if (typeof Storage !== "undefined" && localStorage) {
                localStorage.clear();
            }
            
            // Clear sessionStorage
            if (typeof Storage !== "undefined" && sessionStorage) {
                sessionStorage.clear();
            }
        } catch (e) {
            console.error('Error clearing browser data:', e);
        }
    }
    
    // Method to manually trigger a session check (can be called from other scripts)
    forceCheck() {
        this.checkSessionStatus();
    }
    
    // Method to update check interval (if needed)
    setCheckInterval(intervalMs) {
        this.checkInterval = intervalMs;
        if (this.intervalId) {
            this.stopMonitoring();
            this.startMonitoring();
        }
    }
    
    // Method to check if session is currently invalid
    isSessionInvalid() {
        return this.sessionInvalid;
    }
    
    // Method to update activity timeout (in milliseconds)
    setActivityTimeout(timeoutMs) {
        this.activityTimeout = timeoutMs;
        console.log(`Activity timeout updated to ${timeoutMs / 1000} seconds`);
    }
    
    // Method to get time remaining until timeout
    getTimeUntilTimeout() {
        const timeSinceLastActivity = Date.now() - this.lastActivityTime;
        const remaining = this.activityTimeout - timeSinceLastActivity;
        return Math.max(0, remaining);
    }
    
    // Method to manually reset activity timer
    resetActivityTimer() {
        this.updateLastActivity();
    }
}

// Initialize session monitor when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Create global session monitor instance only if it doesn't exist
    if (!window.sessionMonitor) {
        console.log('Initializing session monitor (DOMContentLoaded)');
        window.sessionMonitor = new SessionMonitor();
    } else {
        console.log('Session monitor already exists (DOMContentLoaded)');
    }
});

// Also handle the case where this script loads after DOMContentLoaded
if (document.readyState !== 'loading') {
    // DOM is already ready
    if (!window.sessionMonitor) {
        console.log('Initializing session monitor (immediate)');
        window.sessionMonitor = new SessionMonitor();
    } else {
        console.log('Session monitor already exists (immediate)');
    }
}

// Export for potential use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionMonitor;
}
