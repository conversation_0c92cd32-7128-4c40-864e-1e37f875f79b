<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GRS Wall Designer - Under Maintenance</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .maintenance-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            margin: 20px;
        }

        .maintenance-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .maintenance-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .maintenance-message {
            font-size: 1rem;
            color: #555;
            margin-bottom: 40px;
            line-height: 1.8;
        }

        .maintenance-features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: left;
        }

        .maintenance-features h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.3rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            color: #555;
            position: relative;
            padding-left: 25px;
        }

        .feature-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .contact-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }

        .contact-info h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .contact-info p {
            color: #555;
            margin: 5px 0;
        }

        .refresh-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .refresh-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #e74c3c;
            border-radius: 50%;
            margin-right: 8px;
            animation: blink 1.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        @media (max-width: 768px) {
            .maintenance-container {
                padding: 40px 20px;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-icon {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">🔧</div>
        
        <h1 class="maintenance-title">Under Maintenance</h1>
        
        <p class="maintenance-subtitle">
            <span class="status-indicator"></span>
            GRS Wall Designer is temporarily unavailable
        </p>
        
        <div class="maintenance-message">
            <p>We're currently performing scheduled maintenance to improve your experience. Our team is working hard to get everything back online as quickly as possible.</p>
        </div>

        <div class="maintenance-features">
            <h3>What we're improving:</h3>
            <ul class="feature-list">
                <li>System performance and reliability</li>
                <li>Database optimization</li>
                <li>Security enhancements</li>
                <li>User interface improvements</li>
                <li>New analysis features</li>
            </ul>
        </div>

        <div class="contact-info">
            <h4>Need immediate assistance?</h4>
            <p>If you have urgent questions or need support, please contact our team:</p>
            <p><strong>Email:</strong> <EMAIL></p>
        </div>

        <button class="refresh-button" onclick="window.location.reload()">
            Check Again
        </button>
        
        <script>
            // Auto-refresh every 30 seconds
            setTimeout(function() {
                window.location.reload();
            }, 30000);
        </script>
    </div>
</body>
</html>
